import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Progress,
  Statistic,
  Table,
  Tag,
  Select,
  DatePicker,
  Button,
  Space,
  Alert,
  Tooltip,
  Timeline,
  Tabs
} from 'antd';
import {
  HeartOutlined,
  TrophyOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  LineChartOutlined,
  <PERSON>boltOutlined,
  WifiOutlined,
  DatabaseOutlined,
  ClockCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

const HealthOverview: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('24h');
  const [selectedRegion, setSelectedRegion] = useState('all');

  // 整体健康度数据
  const overallHealth = {
    score: 95.8,
    trend: '****',
    status: 'excellent',
    lastUpdate: '2024-01-15 14:30:25'
  };

  // 各维度健康度
  const healthDimensions = [
    {
      name: '网络可用性',
      score: 99.95,
      target: 99.9,
      status: 'excellent',
      trend: '+0.05',
      description: '网络设备和链路的可用性'
    },
    {
      name: '性能指标',
      score: 94.2,
      target: 95.0,
      status: 'good',
      trend: '-0.8',
      description: '延迟、吞吐量、丢包率等性能指标'
    },
    {
      name: '安全状况',
      score: 98.1,
      target: 98.0,
      status: 'excellent',
      trend: '****',
      description: '安全事件、威胁检测、防护效果'
    },
    {
      name: '容量利用',
      score: 87.5,
      target: 85.0,
      status: 'warning',
      trend: '****',
      description: '带宽、存储、计算资源利用率'
    },
    {
      name: '故障恢复',
      score: 96.8,
      target: 95.0,
      status: 'excellent',
      trend: '****',
      description: '故障检测和恢复时间'
    },
    {
      name: '服务质量',
      score: 93.4,
      target: 90.0,
      status: 'good',
      trend: '****',
      description: '用户体验和服务满意度'
    }
  ];

  // 区域健康度
  const regionalHealth = [
    {
      key: '1',
      region: '北京',
      overallScore: 97.2,
      availability: 99.98,
      performance: 95.8,
      security: 98.5,
      capacity: 89.2,
      recovery: 97.1,
      quality: 94.8,
      status: 'excellent',
      issues: 2
    },
    {
      key: '2',
      region: '上海',
      overallScore: 95.6,
      availability: 99.92,
      performance: 93.4,
      security: 97.8,
      capacity: 88.1,
      recovery: 96.2,
      quality: 93.2,
      status: 'good',
      issues: 5
    },
    {
      key: '3',
      region: '广州',
      overallScore: 94.1,
      availability: 99.85,
      performance: 91.2,
      security: 98.2,
      capacity: 85.4,
      recovery: 95.8,
      quality: 92.1,
      status: 'good',
      issues: 8
    },
    {
      key: '4',
      region: '深圳',
      overallScore: 89.3,
      availability: 99.12,
      performance: 87.5,
      security: 96.8,
      capacity: 82.1,
      recovery: 91.2,
      quality: 88.9,
      status: 'warning',
      issues: 15
    }
  ];

  // 健康度历史趋势
  const healthHistory = [
    { time: '00:00', score: 94.2 },
    { time: '04:00', score: 95.1 },
    { time: '08:00', score: 96.3 },
    { time: '12:00', score: 95.8 },
    { time: '16:00', score: 94.9 },
    { time: '20:00', score: 95.8 },
    { time: '24:00', score: 95.8 }
  ];

  // 健康事件
  const healthEvents = [
    {
      time: '2024-01-15 14:25',
      type: 'improvement',
      title: '网络性能优化完成',
      description: '北京地区核心网延迟降低15%',
      impact: '****分'
    },
    {
      time: '2024-01-15 13:45',
      type: 'degradation',
      title: '深圳地区容量告警',
      description: '带宽利用率超过85%阈值',
      impact: '-0.8分'
    },
    {
      time: '2024-01-15 12:30',
      type: 'recovery',
      title: '故障恢复完成',
      description: '广州数据中心链路故障已修复',
      impact: '****分'
    },
    {
      time: '2024-01-15 11:15',
      type: 'alert',
      title: '安全威胁检测',
      description: '检测到异常流量，已自动阻断',
      impact: '+0.5分'
    }
  ];

  const getHealthColor = (score: number) => {
    if (score >= 95) return '#52c41a';
    if (score >= 90) return '#1890ff';
    if (score >= 80) return '#faad14';
    return '#ff4d4f';
  };

  const getHealthStatus = (score: number) => {
    if (score >= 95) return { text: '优秀', color: 'green' };
    if (score >= 90) return { text: '良好', color: 'blue' };
    if (score >= 80) return { text: '一般', color: 'orange' };
    return { text: '较差', color: 'red' };
  };

  const getTrendIcon = (trend: string) => {
    if (trend.startsWith('+')) return { icon: '↗', color: '#52c41a' };
    if (trend.startsWith('-')) return { icon: '↘', color: '#ff4d4f' };
    return { icon: '→', color: '#1890ff' };
  };

  const regionalColumns = [
    {
      title: '区域',
      dataIndex: 'region',
      key: 'region',
      width: 80,
    },
    {
      title: '综合评分',
      dataIndex: 'overallScore',
      key: 'overallScore',
      width: 120,
      render: (score: number) => (
        <div>
          <Progress
            percent={score}
            size="small"
            strokeColor={getHealthColor(score)}
            format={() => `${score}%`}
          />
        </div>
      ),
    },
    {
      title: '可用性',
      dataIndex: 'availability',
      key: 'availability',
      width: 100,
      render: (score: number) => `${score}%`,
    },
    {
      title: '性能',
      dataIndex: 'performance',
      key: 'performance',
      width: 80,
      render: (score: number) => `${score}%`,
    },
    {
      title: '安全',
      dataIndex: 'security',
      key: 'security',
      width: 80,
      render: (score: number) => `${score}%`,
    },
    {
      title: '容量',
      dataIndex: 'capacity',
      key: 'capacity',
      width: 80,
      render: (score: number) => `${score}%`,
    },
    {
      title: '恢复',
      dataIndex: 'recovery',
      key: 'recovery',
      width: 80,
      render: (score: number) => `${score}%`,
    },
    {
      title: '质量',
      dataIndex: 'quality',
      key: 'quality',
      width: 80,
      render: (score: number) => `${score}%`,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string, record: any) => {
        const statusInfo = getHealthStatus(record.overallScore);
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
      },
    },
    {
      title: '问题数',
      dataIndex: 'issues',
      key: 'issues',
      width: 80,
      render: (issues: number) => (
        <span style={{ color: issues > 10 ? '#ff4d4f' : issues > 5 ? '#faad14' : '#52c41a' }}>
          {issues}
        </span>
      ),
    }
  ];

  return (
    <div>
      {/* 概览信息 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Alert
            message="网络健康度监控"
            description={`最后更新时间: ${overallHealth.lastUpdate} | 自动评估间隔: 5分钟`}
            type="info"
            showIcon
            action={
              <Button icon={<ReloadOutlined />} size="small">
                立即评估
              </Button>
            }
          />
        </Col>
      </Row>

      {/* 整体健康度 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={8}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Progress
                type="circle"
                percent={overallHealth.score}
                size={120}
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
                format={(percent) => (
                  <div>
                    <div style={{ fontSize: '24px', fontWeight: 'bold' }}>{percent}</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>综合评分</div>
                  </div>
                )}
              />
              <div style={{ marginTop: 16 }}>
                <Tag color="green" icon={<TrophyOutlined />}>
                  {getHealthStatus(overallHealth.score).text}
                </Tag>
                <div style={{ marginTop: 8, color: '#52c41a' }}>
                  较昨日 {overallHealth.trend}
                </div>
              </div>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} lg={16}>
          <Card title="健康度维度分析">
            <Row gutter={16}>
              {healthDimensions.map((dimension, index) => (
                <Col xs={24} sm={12} lg={8} key={index} style={{ marginBottom: 16 }}>
                  <div style={{ padding: '12px', border: '1px solid #f0f0f0', borderRadius: '6px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                      <span style={{ fontWeight: 'bold', fontSize: '14px' }}>{dimension.name}</span>
                      <Tooltip title={dimension.description}>
                        <span style={{ fontSize: '12px', color: '#666' }}>
                          {getTrendIcon(dimension.trend).icon}
                        </span>
                      </Tooltip>
                    </div>
                    <Progress
                      percent={dimension.score}
                      size="small"
                      strokeColor={getHealthColor(dimension.score)}
                      format={() => `${dimension.score}%`}
                    />
                    <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
                      目标: {dimension.target}% | 趋势: 
                      <span style={{ color: getTrendIcon(dimension.trend).color }}>
                        {dimension.trend}
                      </span>
                    </div>
                  </div>
                </Col>
              ))}
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 详细分析 */}
      <Card>
        <Tabs defaultActiveKey="regional">
          <TabPane tab="区域健康度" key="regional">
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Select
                  value={selectedRegion}
                  onChange={setSelectedRegion}
                  style={{ width: 120 }}
                >
                  <Option value="all">全部区域</Option>
                  <Option value="北京">北京</Option>
                  <Option value="上海">上海</Option>
                  <Option value="广州">广州</Option>
                  <Option value="深圳">深圳</Option>
                </Select>
                <RangePicker size="small" />
              </Space>
            </div>
            <Table
              columns={regionalColumns}
              dataSource={regionalHealth.filter(item => 
                selectedRegion === 'all' || item.region === selectedRegion
              )}
              pagination={false}
              scroll={{ x: 800 }}
            />
          </TabPane>

          <TabPane tab="趋势分析" key="trend">
            <Row gutter={16}>
              <Col xs={24} lg={16}>
                <Card title="24小时健康度趋势" size="small">
                  <div style={{ height: '300px', display: 'flex', alignItems: 'end', justifyContent: 'space-around', padding: '20px 0' }}>
                    {healthHistory.map((point, index) => (
                      <div key={index} style={{ textAlign: 'center' }}>
                        <div
                          style={{
                            height: `${point.score * 2}px`,
                            width: '20px',
                            backgroundColor: getHealthColor(point.score),
                            marginBottom: '8px',
                            borderRadius: '2px'
                          }}
                        />
                        <div style={{ fontSize: '12px', color: '#666' }}>{point.time}</div>
                        <div style={{ fontSize: '10px', color: '#999' }}>{point.score}%</div>
                      </div>
                    ))}
                  </div>
                </Card>
              </Col>
              
              <Col xs={24} lg={8}>
                <Card title="关键指标" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Statistic
                      title="平均健康度"
                      value={95.2}
                      suffix="%"
                      prefix={<HeartOutlined />}
                      valueStyle={{ color: '#52c41a' }}
                    />
                    <Statistic
                      title="最高健康度"
                      value={97.2}
                      suffix="%"
                      prefix={<TrophyOutlined />}
                      valueStyle={{ color: '#1890ff' }}
                    />
                    <Statistic
                      title="改善次数"
                      value={12}
                      prefix={<CheckCircleOutlined />}
                      valueStyle={{ color: '#52c41a' }}
                    />
                    <Statistic
                      title="告警次数"
                      value={3}
                      prefix={<WarningOutlined />}
                      valueStyle={{ color: '#faad14' }}
                    />
                  </Space>
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="健康事件" key="events">
            <Timeline>
              {healthEvents.map((event, index) => (
                <Timeline.Item
                  key={index}
                  color={
                    event.type === 'improvement' ? 'green' :
                    event.type === 'degradation' ? 'red' :
                    event.type === 'recovery' ? 'blue' : 'orange'
                  }
                  dot={
                    event.type === 'improvement' ? <CheckCircleOutlined /> :
                    event.type === 'degradation' ? <WarningOutlined /> :
                    event.type === 'recovery' ? <ReloadOutlined /> :
                    <ClockCircleOutlined />
                  }
                >
                  <div>
                    <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
                      {event.title}
                      <Tag 
                        style={{ marginLeft: 8 }}
                        color={
                          event.type === 'improvement' ? 'green' :
                          event.type === 'degradation' ? 'red' :
                          event.type === 'recovery' ? 'blue' : 'orange'
                        }
                      >
                        {event.impact}
                      </Tag>
                    </div>
                    <div style={{ color: '#666', marginBottom: 4 }}>{event.description}</div>
                    <div style={{ fontSize: '12px', color: '#999' }}>{event.time}</div>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default HealthOverview;
