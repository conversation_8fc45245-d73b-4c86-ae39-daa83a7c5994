import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Select,
  DatePicker,
  Checkbox,
  Form,
  Input,
  Space,
  Alert,
  Progress,
  Tag,
  Table,
  Typography,
  Steps,
  message,
  Upload,
  Divider
} from 'antd';
import {
  DownloadOutlined,
  FileExcelOutlined,
  FilePdfOutlined,
  FileTextOutlined,
  CloudDownloadOutlined,
  ScheduleOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  LoadingOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Step } = Steps;
const { Title, Text } = Typography;
const { CheckboxGroup } = Checkbox;

const DataExport: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [exportForm] = Form.useForm();
  const [selectedTables, setSelectedTables] = useState<string[]>([]);
  const [exportFormat, setExportFormat] = useState('excel');

  // 可导出的数据表
  const availableTables = [
    {
      key: 'customers',
      name: '客户信息表',
      description: '包含客户基本信息、联系方式、合同信息等',
      recordCount: 3414,
      size: '2.5MB',
      lastUpdate: '2024-01-15 14:30'
    },
    {
      key: 'services',
      name: '服务配置表',
      description: '客户服务配置、SLA协议、服务状态等',
      recordCount: 8956,
      size: '5.2MB',
      lastUpdate: '2024-01-15 14:25'
    },
    {
      key: 'performance',
      name: '性能监控数据',
      description: '网络性能指标、设备监控数据、告警记录',
      recordCount: 156780,
      size: '45.8MB',
      lastUpdate: '2024-01-15 14:30'
    },
    {
      key: 'incidents',
      name: '故障事件表',
      description: '故障记录、处理过程、解决方案等',
      recordCount: 2341,
      size: '1.8MB',
      lastUpdate: '2024-01-15 14:20'
    },
    {
      key: 'revenue',
      name: '收入统计表',
      description: '收入明细、账单信息、财务数据',
      recordCount: 12456,
      size: '3.2MB',
      lastUpdate: '2024-01-15 14:15'
    },
    {
      key: 'quality',
      name: '服务质量数据',
      description: '客户满意度、SLA达成率、质量评估',
      recordCount: 5678,
      size: '2.1MB',
      lastUpdate: '2024-01-15 14:10'
    }
  ];

  // 导出历史记录
  const exportHistory = [
    {
      key: '1',
      taskName: '月度业务报表',
      exportType: '客户信息+收入统计',
      format: 'Excel',
      status: 'completed',
      createTime: '2024-01-15 10:30',
      completeTime: '2024-01-15 10:32',
      fileSize: '8.5MB',
      downloadCount: 5
    },
    {
      key: '2',
      taskName: '性能监控数据',
      exportType: '性能监控数据',
      format: 'CSV',
      status: 'processing',
      createTime: '2024-01-15 14:20',
      completeTime: '-',
      fileSize: '-',
      downloadCount: 0
    },
    {
      key: '3',
      taskName: '故障分析报告',
      exportType: '故障事件+服务质量',
      format: 'PDF',
      status: 'failed',
      createTime: '2024-01-15 09:15',
      completeTime: '-',
      fileSize: '-',
      downloadCount: 0
    },
    {
      key: '4',
      taskName: '客户满意度调研',
      exportType: '服务质量数据',
      format: 'Excel',
      status: 'completed',
      createTime: '2024-01-14 16:45',
      completeTime: '2024-01-14 16:47',
      fileSize: '2.1MB',
      downloadCount: 12
    }
  ];

  // 定时导出任务
  const scheduledTasks = [
    {
      key: '1',
      taskName: '日报自动导出',
      schedule: '每日 08:00',
      dataType: '性能监控数据',
      format: 'Excel',
      status: 'active',
      nextRun: '2024-01-16 08:00',
      lastRun: '2024-01-15 08:00'
    },
    {
      key: '2',
      taskName: '周报自动导出',
      schedule: '每周一 09:00',
      dataType: '客户信息+收入统计',
      format: 'PDF',
      status: 'active',
      nextRun: '2024-01-22 09:00',
      lastRun: '2024-01-15 09:00'
    },
    {
      key: '3',
      taskName: '月报自动导出',
      schedule: '每月1日 10:00',
      dataType: '全量业务数据',
      format: 'Excel',
      status: 'paused',
      nextRun: '2024-02-01 10:00',
      lastRun: '2024-01-01 10:00'
    }
  ];

  const getStatusColor = (status: string) => {
    const colors = {
      completed: 'green',
      processing: 'blue',
      failed: 'red',
      active: 'green',
      paused: 'orange'
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getStatusText = (status: string) => {
    const texts = {
      completed: '已完成',
      processing: '处理中',
      failed: '失败',
      active: '活跃',
      paused: '暂停'
    };
    return texts[status as keyof typeof texts] || '未知';
  };

  const getStatusIcon = (status: string) => {
    const icons = {
      completed: <CheckCircleOutlined />,
      processing: <LoadingOutlined />,
      failed: <CloseCircleOutlined />,
      active: <CheckCircleOutlined />,
      paused: <ClockCircleOutlined />
    };
    return icons[status as keyof typeof icons] || <ClockCircleOutlined />;
  };

  const tableColumns = [
    {
      title: '数据表',
      key: 'tableInfo',
      render: (_, record: any) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.name}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.description}</div>
        </div>
      ),
    },
    {
      title: '记录数',
      dataIndex: 'recordCount',
      key: 'recordCount',
      width: 100,
      render: (count: number) => count.toLocaleString(),
    },
    {
      title: '数据大小',
      dataIndex: 'size',
      key: 'size',
      width: 100,
    },
    {
      title: '最后更新',
      dataIndex: 'lastUpdate',
      key: 'lastUpdate',
      width: 150,
    },
    {
      title: '选择',
      key: 'select',
      width: 80,
      render: (_, record: any) => (
        <Checkbox
          checked={selectedTables.includes(record.key)}
          onChange={(e) => {
            if (e.target.checked) {
              setSelectedTables([...selectedTables, record.key]);
            } else {
              setSelectedTables(selectedTables.filter(key => key !== record.key));
            }
          }}
        />
      ),
    }
  ];

  const historyColumns = [
    {
      title: '任务名称',
      dataIndex: 'taskName',
      key: 'taskName',
      width: 150,
    },
    {
      title: '导出类型',
      dataIndex: 'exportType',
      key: 'exportType',
      width: 200,
    },
    {
      title: '格式',
      dataIndex: 'format',
      key: 'format',
      width: 80,
      render: (format: string) => (
        <Tag icon={
          format === 'Excel' ? <FileExcelOutlined /> :
          format === 'PDF' ? <FilePdfOutlined /> :
          <FileTextOutlined />
        }>
          {format}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
      width: 100,
    },
    {
      title: '下载次数',
      dataIndex: 'downloadCount',
      key: 'downloadCount',
      width: 100,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record: any) => (
        <Space size="small">
          {record.status === 'completed' && (
            <Button
              type="link"
              icon={<DownloadOutlined />}
              size="small"
              onClick={() => message.success('开始下载文件')}
            >
              下载
            </Button>
          )}
          <Button
            type="link"
            size="small"
            onClick={() => message.info('查看详情')}
          >
            详情
          </Button>
        </Space>
      ),
    }
  ];

  const scheduleColumns = [
    {
      title: '任务名称',
      dataIndex: 'taskName',
      key: 'taskName',
      width: 150,
    },
    {
      title: '执行计划',
      dataIndex: 'schedule',
      key: 'schedule',
      width: 120,
    },
    {
      title: '数据类型',
      dataIndex: 'dataType',
      key: 'dataType',
      width: 200,
    },
    {
      title: '格式',
      dataIndex: 'format',
      key: 'format',
      width: 80,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '下次执行',
      dataIndex: 'nextRun',
      key: 'nextRun',
      width: 150,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record: any) => (
        <Space size="small">
          <Button type="link" size="small">编辑</Button>
          <Button type="link" size="small">
            {record.status === 'active' ? '暂停' : '启用'}
          </Button>
          <Button type="link" size="small" danger>删除</Button>
        </Space>
      ),
    }
  ];

  const handleExport = () => {
    if (selectedTables.length === 0) {
      message.warning('请至少选择一个数据表');
      return;
    }
    message.success('导出任务已创建，正在处理中...');
    setCurrentStep(0);
  };

  const steps = [
    {
      title: '选择数据',
      description: '选择要导出的数据表和字段',
    },
    {
      title: '配置选项',
      description: '设置导出格式和筛选条件',
    },
    {
      title: '确认导出',
      description: '确认导出配置并开始处理',
    }
  ];

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Alert
            message="数据导出中心"
            description="支持多种格式的数据导出，包括Excel、CSV、PDF等，支持定时导出和批量处理"
            type="info"
            showIcon
          />
        </Col>
      </Row>

      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={16}>
          <Card title="数据导出向导">
            <Steps current={currentStep} style={{ marginBottom: 24 }}>
              {steps.map((step, index) => (
                <Step
                  key={index}
                  title={step.title}
                  description={step.description}
                />
              ))}
            </Steps>

            {currentStep === 0 && (
              <div>
                <Title level={4}>选择要导出的数据表</Title>
                <Table
                  columns={tableColumns}
                  dataSource={availableTables}
                  pagination={false}
                  size="small"
                />
                <div style={{ marginTop: 16, textAlign: 'right' }}>
                  <Button
                    type="primary"
                    onClick={() => setCurrentStep(1)}
                    disabled={selectedTables.length === 0}
                  >
                    下一步
                  </Button>
                </div>
              </div>
            )}

            {currentStep === 1 && (
              <div>
                <Title level={4}>配置导出选项</Title>
                <Form form={exportForm} layout="vertical">
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item label="导出格式" name="format">
                        <Select defaultValue="excel" onChange={setExportFormat}>
                          <Option value="excel">
                            <FileExcelOutlined /> Excel (.xlsx)
                          </Option>
                          <Option value="csv">
                            <FileTextOutlined /> CSV (.csv)
                          </Option>
                          <Option value="pdf">
                            <FilePdfOutlined /> PDF (.pdf)
                          </Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="时间范围" name="dateRange">
                        <RangePicker style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Form.Item label="文件名称" name="fileName">
                    <Input placeholder="请输入文件名称" />
                  </Form.Item>
                  <Form.Item label="导出选项" name="options">
                    <CheckboxGroup>
                      <Checkbox value="includeHeader">包含表头</Checkbox>
                      <Checkbox value="compressFile">压缩文件</Checkbox>
                      <Checkbox value="emailNotify">邮件通知</Checkbox>
                    </CheckboxGroup>
                  </Form.Item>
                </Form>
                <div style={{ marginTop: 16, textAlign: 'right' }}>
                  <Space>
                    <Button onClick={() => setCurrentStep(0)}>上一步</Button>
                    <Button type="primary" onClick={() => setCurrentStep(2)}>
                      下一步
                    </Button>
                  </Space>
                </div>
              </div>
            )}

            {currentStep === 2 && (
              <div>
                <Title level={4}>确认导出配置</Title>
                <div style={{ background: '#fafafa', padding: 16, borderRadius: 6, marginBottom: 16 }}>
                  <p><strong>选择的数据表：</strong>{selectedTables.length} 个</p>
                  <p><strong>导出格式：</strong>{exportFormat.toUpperCase()}</p>
                  <p><strong>预计文件大小：</strong>约 15.2MB</p>
                  <p><strong>预计处理时间：</strong>2-3分钟</p>
                </div>
                <div style={{ marginTop: 16, textAlign: 'right' }}>
                  <Space>
                    <Button onClick={() => setCurrentStep(1)}>上一步</Button>
                    <Button type="primary" icon={<DownloadOutlined />} onClick={handleExport}>
                      开始导出
                    </Button>
                  </Space>
                </div>
              </div>
            )}
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="快速导出" size="small" style={{ marginBottom: 16 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button block icon={<FileExcelOutlined />}>
                导出客户信息表
              </Button>
              <Button block icon={<BarChartOutlined />}>
                导出性能监控数据
              </Button>
              <Button block icon={<FilePdfOutlined />}>
                导出月度报表
              </Button>
              <Button block icon={<CloudDownloadOutlined />}>
                导出全量数据
              </Button>
            </Space>
          </Card>

          <Card title="导出统计" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>今日导出：</span>
                <span><strong>12</strong> 次</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>本月导出：</span>
                <span><strong>156</strong> 次</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>总数据量：</span>
                <span><strong>2.5GB</strong></span>
              </div>
              <Progress percent={75} size="small" />
              <Text type="secondary" style={{ fontSize: '12px' }}>
                存储空间使用率 75%
              </Text>
            </Space>
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col xs={24} lg={12}>
          <Card title="导出历史" extra={<Button size="small">清理历史</Button>}>
            <Table
              columns={historyColumns}
              dataSource={exportHistory}
              pagination={false}
              size="small"
              scroll={{ x: 800 }}
            />
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card
            title="定时导出任务"
            extra={
              <Button
                type="primary"
                size="small"
                icon={<ScheduleOutlined />}
              >
                新建任务
              </Button>
            }
          >
            <Table
              columns={scheduleColumns}
              dataSource={scheduledTasks}
              pagination={false}
              size="small"
              scroll={{ x: 800 }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DataExport;
