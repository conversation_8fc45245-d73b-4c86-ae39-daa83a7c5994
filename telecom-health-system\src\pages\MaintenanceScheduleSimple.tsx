import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Table,
  Tag,
  Space,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Alert,
  Calendar,
  Badge,
  message
} from 'antd';
import {
  PlusOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  ToolOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { TextArea } = Input;

const MaintenanceScheduleSimple: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedMaintenance, setSelectedMaintenance] = useState(null);
  const [form] = Form.useForm();

  const maintenanceData = [
    {
      key: '1',
      id: 'MT-2024-001',
      title: '核心路由器系统升级',
      type: '系统升级',
      priority: 'high',
      status: 'scheduled',
      deviceName: 'RT-BJ-001',
      location: '北京数据中心A栋',
      scheduledTime: '2024-01-20 02:00',
      duration: 4,
      assignee: '张工程师',
      description: '升级路由器操作系统到最新版本',
      impact: '可能影响北京地区网络服务',
      approver: '李主管',
      createTime: '2024-01-15 10:30'
    },
    {
      key: '2',
      id: 'MT-2024-002',
      title: '防火墙规则更新',
      type: '配置变更',
      priority: 'medium',
      status: 'in-progress',
      deviceName: 'FW-SH-008',
      location: '上海数据中心B栋',
      scheduledTime: '2024-01-16 01:00',
      duration: 2,
      assignee: '王技术员',
      description: '更新防火墙安全规则',
      impact: '短暂影响安全策略',
      approver: '张主管',
      createTime: '2024-01-14 15:20'
    },
    {
      key: '3',
      id: 'MT-2024-003',
      title: '交换机硬件更换',
      type: '硬件更换',
      priority: 'high',
      status: 'completed',
      deviceName: 'SW-GZ-015',
      location: '广州数据中心C栋',
      scheduledTime: '2024-01-14 03:00',
      duration: 6,
      assignee: '刘工程师',
      description: '更换故障交换机模块',
      impact: '影响部分客户网络连接',
      approver: '陈主管',
      createTime: '2024-01-13 09:15'
    }
  ];

  const getStatusColor = (status: string) => {
    const colors = {
      scheduled: 'blue',
      'in-progress': 'orange',
      completed: 'green',
      cancelled: 'red'
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      high: 'red',
      medium: 'orange',
      low: 'green'
    };
    return colors[priority as keyof typeof colors] || 'default';
  };

  const columns = [
    {
      title: '维护信息',
      key: 'maintenanceInfo',
      render: (_, record: any) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.title}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.id} | {record.type}
          </div>
        </div>
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 100,
      render: (priority: string) => (
        <Tag color={getPriorityColor(priority)}>
          {priority === 'high' ? '高' : priority === 'medium' ? '中' : '低'}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status === 'scheduled' ? '已计划' :
           status === 'in-progress' ? '进行中' :
           status === 'completed' ? '已完成' : '已取消'}
        </Tag>
      ),
    },
    {
      title: '设备/位置',
      key: 'device',
      render: (_, record: any) => (
        <div>
          <div>{record.deviceName}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.location}</div>
        </div>
      ),
    },
    {
      title: '计划时间',
      dataIndex: 'scheduledTime',
      key: 'scheduledTime',
      width: 150,
    },
    {
      title: '持续时间',
      dataIndex: 'duration',
      key: 'duration',
      width: 100,
      render: (duration: number) => `${duration}小时`,
    },
    {
      title: '负责人',
      dataIndex: 'assignee',
      key: 'assignee',
      width: 100,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record: any) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          />
        </Space>
      ),
    }
  ];

  const handleEdit = (record: any) => {
    setSelectedMaintenance(record);
    form.setFieldsValue(record);
    setIsModalVisible(true);
  };

  const handleDelete = (record: any) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除维护计划 "${record.title}" 吗？`,
      onOk() {
        message.success('删除成功');
      },
    });
  };

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Alert
            message="维护计划管理"
            description="统一管理设备维护计划，确保维护工作有序进行"
            type="info"
            showIcon
          />
        </Col>
      </Row>

      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={16}>
          <Card
            title="维护计划列表"
            extra={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setSelectedMaintenance(null);
                  form.resetFields();
                  setIsModalVisible(true);
                }}
              >
                新建维护计划
              </Button>
            }
          >
            <Table
              columns={columns}
              dataSource={maintenanceData}
              pagination={false}
              scroll={{ x: 1000 }}
            />
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="维护日历" size="small">
            <Calendar
              fullscreen={false}
              dateCellRender={(value) => {
                const hasSchedule = Math.random() > 0.8;
                return hasSchedule ? (
                  <Badge status="processing" />
                ) : null;
              }}
            />
          </Card>
        </Col>
      </Row>

      <Modal
        title={selectedMaintenance ? '编辑维护计划' : '新建维护计划'}
        open={isModalVisible}
        onOk={() => {
          form.validateFields().then(() => {
            message.success(selectedMaintenance ? '更新成功' : '创建成功');
            setIsModalVisible(false);
          });
        }}
        onCancel={() => setIsModalVisible(false)}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item name="title" label="维护标题" rules={[{ required: true }]}>
            <Input placeholder="请输入维护标题" />
          </Form.Item>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="type" label="维护类型" rules={[{ required: true }]}>
                <Select placeholder="请选择维护类型">
                  <Option value="系统升级">系统升级</Option>
                  <Option value="配置变更">配置变更</Option>
                  <Option value="硬件更换">硬件更换</Option>
                  <Option value="预防性维护">预防性维护</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="priority" label="优先级" rules={[{ required: true }]}>
                <Select placeholder="请选择优先级">
                  <Option value="high">高</Option>
                  <Option value="medium">中</Option>
                  <Option value="low">低</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item name="description" label="维护描述">
            <TextArea rows={3} placeholder="请输入维护描述" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default MaintenanceScheduleSimple;
