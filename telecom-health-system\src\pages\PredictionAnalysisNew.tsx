import React, { useState } from 'react';
import { Card, Row, Col, Statistic, Table, Select, Alert, Progress, Tag, Tabs } from 'antd';
import { RobotOutlined, LineChartOutlined, WarningOutlined, DashboardOutlined, BulbOutlined, CheckCircleOutlined } from '@ant-design/icons';

const { Option } = Select;
const { TabPane } = Tabs;

const PredictionAnalysisNew: React.FC = () => {
  const [selectedModel, setSelectedModel] = useState('all');

  const predictionOverview = {
    totalModels: 8,
    activeModels: 6,
    accuracy: 89.5,
    predictions: 156,
    alerts: 23,
    recommendations: 45
  };

  const predictionResults = [
    {
      key: '1',
      modelName: '网络容量预测',
      targetMetric: '带宽利用率',
      currentValue: 75.8,
      predictedValue: 92.3,
      confidence: 89.5,
      riskLevel: 'high',
      recommendation: '建议在3天内进行带宽扩容'
    },
    {
      key: '2',
      modelName: '设备故障预测',
      targetMetric: 'CPU温度',
      currentValue: 68.2,
      predictedValue: 85.6,
      confidence: 92.1,
      riskLevel: 'medium',
      recommendation: '建议检查散热系统'
    }
  ];

  const getRiskColor = (level: string) => {
    const colors = { critical: '#ff4d4f', high: '#faad14', medium: '#1890ff', low: '#52c41a' };
    return colors[level as keyof typeof colors] || '#d9d9d9';
  };

  const getRiskText = (level: string) => {
    const texts = { critical: '严重', high: '高风险', medium: '中风险', low: '低风险' };
    return texts[level as keyof typeof texts] || '未知';
  };

  const columns = [
    {
      title: '预测模型',
      key: 'modelInfo',
      render: (_, record: any) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.modelName}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>目标指标: {record.targetMetric}</div>
        </div>
      ),
    },
    {
      title: '当前值',
      dataIndex: 'currentValue',
      key: 'currentValue',
      render: (value: number) => <div style={{ fontWeight: 'bold' }}>{value}</div>,
    },
    {
      title: '预测值',
      dataIndex: 'predictedValue',
      key: 'predictedValue',
      render: (value: number) => <div style={{ fontWeight: 'bold' }}>{value}</div>,
    },
    {
      title: '置信度',
      dataIndex: 'confidence',
      key: 'confidence',
      render: (confidence: number) => (
        <Progress
          percent={confidence}
          size="small"
          strokeColor={confidence >= 90 ? '#52c41a' : confidence >= 80 ? '#1890ff' : '#faad14'}
          format={(percent) => `${percent}%`}
        />
      ),
    },
    {
      title: '风险等级',
      dataIndex: 'riskLevel',
      key: 'riskLevel',
      render: (level: string) => (
        <Tag color={getRiskColor(level)}>{getRiskText(level)}</Tag>
      ),
    },
    {
      title: '建议措施',
      dataIndex: 'recommendation',
      key: 'recommendation',
      ellipsis: true,
    }
  ];

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Alert
            message="AI预测分析"
            description="基于机器学习算法，预测网络性能趋势、故障风险和容量需求"
            type="info"
            showIcon
          />
        </Col>
      </Row>

      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={8} lg={4}>
          <Card>
            <Statistic
              title="预测模型"
              value={predictionOverview.totalModels}
              prefix={<RobotOutlined />}
              valueStyle={{ color: '#1890ff' }}
              suffix={`/${predictionOverview.activeModels}活跃`}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8} lg={4}>
          <Card>
            <Statistic
              title="平均准确率"
              value={predictionOverview.accuracy}
              suffix="%"
              precision={1}
              prefix={<DashboardOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8} lg={4}>
          <Card>
            <Statistic
              title="预测结果"
              value={predictionOverview.predictions}
              prefix={<LineChartOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8} lg={4}>
          <Card>
            <Statistic
              title="预测告警"
              value={predictionOverview.alerts}
              prefix={<WarningOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8} lg={4}>
          <Card>
            <Statistic
              title="AI建议"
              value={predictionOverview.recommendations}
              prefix={<BulbOutlined />}
              valueStyle={{ color: '#13c2c2' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8} lg={4}>
          <Card>
            <Statistic
              title="模型状态"
              value="正常"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <Tabs defaultActiveKey="predictions">
          <TabPane tab="预测结果" key="predictions">
            <div style={{ marginBottom: 16 }}>
              <Select
                value={selectedModel}
                onChange={setSelectedModel}
                style={{ width: 150 }}
              >
                <Option value="all">全部模型</Option>
                <Option value="capacity">容量预测</Option>
                <Option value="failure">故障预测</Option>
                <Option value="traffic">流量预测</Option>
                <Option value="quality">质量预测</Option>
              </Select>
            </div>
            <Table
              columns={columns}
              dataSource={predictionResults}
              pagination={false}
              scroll={{ x: 1000 }}
            />
          </TabPane>
          <TabPane tab="预测告警" key="alerts">
            <div style={{ padding: '20px', textAlign: 'center', color: '#999' }}>
              预测告警数据加载中...
            </div>
          </TabPane>
          <TabPane tab="AI建议" key="recommendations">
            <div style={{ padding: '20px', textAlign: 'center', color: '#999' }}>
              AI建议数据加载中...
            </div>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default PredictionAnalysisNew;
