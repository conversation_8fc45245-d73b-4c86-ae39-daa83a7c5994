import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Table,
  Tag,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Alert,
  Statistic,
  Timeline,
  Descriptions,
  message,
  Upload
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  FileTextOutlined,
  UploadOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { TextArea } = Input;

const TicketManagement: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [form] = Form.useForm();

  const ticketStats = {
    total: 156,
    open: 45,
    inProgress: 67,
    resolved: 44,
    avgResolutionTime: 4.5
  };

  const ticketData = [
    {
      key: '1',
      id: 'TK-2024-001',
      title: '北京核心路由器性能异常',
      type: '故障报告',
      priority: 'high',
      status: 'open',
      reporter: '张经理',
      assignee: '李工程师',
      createTime: '2024-01-15 14:30',
      updateTime: '2024-01-15 14:30',
      description: '核心路由器CPU使用率持续超过90%',
      category: '性能问题',
      impact: '高',
      urgency: '高'
    },
    {
      key: '2',
      id: 'TK-2024-002',
      title: '客户专线带宽扩容申请',
      type: '服务请求',
      priority: 'medium',
      status: 'in-progress',
      reporter: '王客户',
      assignee: '刘技术员',
      createTime: '2024-01-15 10:15',
      updateTime: '2024-01-15 13:20',
      description: '申请将专线带宽从100M扩容到500M',
      category: '容量变更',
      impact: '中',
      urgency: '中'
    }
  ];

  const getStatusColor = (status: string) => {
    const colors = {
      open: 'red',
      'in-progress': 'orange',
      resolved: 'green',
      closed: 'default'
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      high: 'red',
      medium: 'orange',
      low: 'green'
    };
    return colors[priority as keyof typeof colors] || 'default';
  };

  const columns = [
    {
      title: '工单信息',
      key: 'ticketInfo',
      render: (_, record: any) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.title}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.id} | {record.type}
          </div>
        </div>
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 100,
      render: (priority: string) => (
        <Tag color={getPriorityColor(priority)}>
          {priority === 'high' ? '高' : priority === 'medium' ? '中' : '低'}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status === 'open' ? '待处理' :
           status === 'in-progress' ? '处理中' :
           status === 'resolved' ? '已解决' : '已关闭'}
        </Tag>
      ),
    },
    {
      title: '报告人',
      dataIndex: 'reporter',
      key: 'reporter',
      width: 100,
    },
    {
      title: '处理人',
      dataIndex: 'assignee',
      key: 'assignee',
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 150,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record: any) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => showTicketDetail(record)}
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          />
        </Space>
      ),
    }
  ];

  const showTicketDetail = (ticket: any) => {
    setSelectedTicket(ticket);
    setDetailModalVisible(true);
  };

  const handleEdit = (ticket: any) => {
    setSelectedTicket(ticket);
    form.setFieldsValue(ticket);
    setIsModalVisible(true);
  };

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Alert
            message="工单管理系统"
            description="统一管理客户服务请求和故障报告，确保及时响应和处理"
            type="info"
            showIcon
          />
        </Col>
      </Row>

      {/* 工单统计 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="工单总数"
              value={ticketStats.total}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="待处理"
              value={ticketStats.open}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="处理中"
              value={ticketStats.inProgress}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="平均解决时间"
              value={ticketStats.avgResolutionTime}
              suffix="小时"
              precision={1}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      <Card
        title="工单列表"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setSelectedTicket(null);
              form.resetFields();
              setIsModalVisible(true);
            }}
          >
            新建工单
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={ticketData}
          pagination={{
            total: ticketData.length,
            pageSize: 10,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* 新建/编辑工单模态框 */}
      <Modal
        title={selectedTicket ? '编辑工单' : '新建工单'}
        open={isModalVisible}
        onOk={() => {
          form.validateFields().then(() => {
            message.success(selectedTicket ? '更新成功' : '创建成功');
            setIsModalVisible(false);
          });
        }}
        onCancel={() => setIsModalVisible(false)}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item name="title" label="工单标题" rules={[{ required: true }]}>
            <Input placeholder="请输入工单标题" />
          </Form.Item>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="type" label="工单类型" rules={[{ required: true }]}>
                <Select placeholder="请选择工单类型">
                  <Option value="故障报告">故障报告</Option>
                  <Option value="服务请求">服务请求</Option>
                  <Option value="变更申请">变更申请</Option>
                  <Option value="咨询问题">咨询问题</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="priority" label="优先级" rules={[{ required: true }]}>
                <Select placeholder="请选择优先级">
                  <Option value="high">高</Option>
                  <Option value="medium">中</Option>
                  <Option value="low">低</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item name="description" label="问题描述" rules={[{ required: true }]}>
            <TextArea rows={4} placeholder="请详细描述问题" />
          </Form.Item>
          <Form.Item name="assignee" label="指派给">
            <Select placeholder="请选择处理人">
              <Option value="李工程师">李工程师</Option>
              <Option value="王技术员">王技术员</Option>
              <Option value="刘运维">刘运维</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 工单详情模态框 */}
      <Modal
        title="工单详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedTicket && (
          <div>
            <Descriptions column={2} bordered>
              <Descriptions.Item label="工单ID">{(selectedTicket as any).id}</Descriptions.Item>
              <Descriptions.Item label="工单标题">{(selectedTicket as any).title}</Descriptions.Item>
              <Descriptions.Item label="工单类型">{(selectedTicket as any).type}</Descriptions.Item>
              <Descriptions.Item label="优先级">
                <Tag color={getPriorityColor((selectedTicket as any).priority)}>
                  {(selectedTicket as any).priority === 'high' ? '高' : 
                   (selectedTicket as any).priority === 'medium' ? '中' : '低'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={getStatusColor((selectedTicket as any).status)}>
                  {(selectedTicket as any).status === 'open' ? '待处理' :
                   (selectedTicket as any).status === 'in-progress' ? '处理中' :
                   (selectedTicket as any).status === 'resolved' ? '已解决' : '已关闭'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="报告人">{(selectedTicket as any).reporter}</Descriptions.Item>
              <Descriptions.Item label="处理人">{(selectedTicket as any).assignee}</Descriptions.Item>
              <Descriptions.Item label="创建时间">{(selectedTicket as any).createTime}</Descriptions.Item>
              <Descriptions.Item label="问题描述" span={2}>
                {(selectedTicket as any).description}
              </Descriptions.Item>
            </Descriptions>
            
            <div style={{ marginTop: 24 }}>
              <h4>处理记录</h4>
              <Timeline>
                <Timeline.Item color="blue">
                  <div>
                    <strong>工单已创建</strong>
                    <div style={{ color: '#666' }}>由 {(selectedTicket as any).reporter} 创建</div>
                    <div style={{ fontSize: '12px', color: '#999' }}>{(selectedTicket as any).createTime}</div>
                  </div>
                </Timeline.Item>
                <Timeline.Item color="orange">
                  <div>
                    <strong>工单已分配</strong>
                    <div style={{ color: '#666' }}>分配给 {(selectedTicket as any).assignee}</div>
                    <div style={{ fontSize: '12px', color: '#999' }}>{(selectedTicket as any).updateTime}</div>
                  </div>
                </Timeline.Item>
              </Timeline>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default TicketManagement;
