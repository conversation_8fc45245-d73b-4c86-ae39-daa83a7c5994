import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Select,
  DatePicker,
  Form,
  Input,
  Space,
  Alert,
  Table,
  Typography,
  Steps,
  message
} from 'antd';
import {
  DownloadOutlined,
  FileExcelOutlined,
  FilePdfOutlined,
  FileTextOutlined,
  CloudDownloadOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Step } = Steps;
const { Title } = Typography;

const DataExportSimple: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [exportForm] = Form.useForm();
  const [selectedTables, setSelectedTables] = useState<string[]>([]);

  const availableTables = [
    {
      key: 'customers',
      name: '客户信息表',
      description: '包含客户基本信息、联系方式、合同信息等',
      recordCount: 3414,
      size: '2.5MB',
      lastUpdate: '2024-01-15 14:30'
    },
    {
      key: 'services',
      name: '服务配置表',
      description: '客户服务配置、SLA协议、服务状态等',
      recordCount: 8956,
      size: '5.2MB',
      lastUpdate: '2024-01-15 14:25'
    },
    {
      key: 'performance',
      name: '性能监控数据',
      description: '网络性能指标、设备监控数据、告警记录',
      recordCount: 156780,
      size: '45.8MB',
      lastUpdate: '2024-01-15 14:30'
    }
  ];

  const tableColumns = [
    {
      title: '数据表',
      key: 'tableInfo',
      render: (_, record: any) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.name}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.description}</div>
        </div>
      ),
    },
    {
      title: '记录数',
      dataIndex: 'recordCount',
      key: 'recordCount',
      width: 100,
      render: (count: number) => count.toLocaleString(),
    },
    {
      title: '数据大小',
      dataIndex: 'size',
      key: 'size',
      width: 100,
    },
    {
      title: '最后更新',
      dataIndex: 'lastUpdate',
      key: 'lastUpdate',
      width: 150,
    }
  ];

  const steps = [
    {
      title: '选择数据',
      description: '选择要导出的数据表和字段',
    },
    {
      title: '配置选项',
      description: '设置导出格式和筛选条件',
    },
    {
      title: '确认导出',
      description: '确认导出配置并开始处理',
    }
  ];

  const handleExport = () => {
    message.success('导出任务已创建，正在处理中...');
    setCurrentStep(0);
  };

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Alert
            message="数据导出中心"
            description="支持多种格式的数据导出，包括Excel、CSV、PDF等，支持定时导出和批量处理"
            type="info"
            showIcon
          />
        </Col>
      </Row>

      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={16}>
          <Card title="数据导出向导">
            <Steps current={currentStep} style={{ marginBottom: 24 }}>
              {steps.map((step, index) => (
                <Step
                  key={index}
                  title={step.title}
                  description={step.description}
                />
              ))}
            </Steps>

            {currentStep === 0 && (
              <div>
                <Title level={4}>选择要导出的数据表</Title>
                <Table
                  columns={tableColumns}
                  dataSource={availableTables}
                  pagination={false}
                  size="small"
                />
                <div style={{ marginTop: 16, textAlign: 'right' }}>
                  <Button
                    type="primary"
                    onClick={() => setCurrentStep(1)}
                  >
                    下一步
                  </Button>
                </div>
              </div>
            )}

            {currentStep === 1 && (
              <div>
                <Title level={4}>配置导出选项</Title>
                <Form form={exportForm} layout="vertical">
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item label="导出格式" name="format">
                        <Select defaultValue="excel">
                          <Option value="excel">
                            <FileExcelOutlined /> Excel (.xlsx)
                          </Option>
                          <Option value="csv">
                            <FileTextOutlined /> CSV (.csv)
                          </Option>
                          <Option value="pdf">
                            <FilePdfOutlined /> PDF (.pdf)
                          </Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="时间范围" name="dateRange">
                        <RangePicker style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Form.Item label="文件名称" name="fileName">
                    <Input placeholder="请输入文件名称" />
                  </Form.Item>
                </Form>
                <div style={{ marginTop: 16, textAlign: 'right' }}>
                  <Space>
                    <Button onClick={() => setCurrentStep(0)}>上一步</Button>
                    <Button type="primary" onClick={() => setCurrentStep(2)}>
                      下一步
                    </Button>
                  </Space>
                </div>
              </div>
            )}

            {currentStep === 2 && (
              <div>
                <Title level={4}>确认导出配置</Title>
                <div style={{ background: '#fafafa', padding: 16, borderRadius: 6, marginBottom: 16 }}>
                  <p><strong>选择的数据表：</strong>3 个</p>
                  <p><strong>导出格式：</strong>Excel</p>
                  <p><strong>预计文件大小：</strong>约 15.2MB</p>
                  <p><strong>预计处理时间：</strong>2-3分钟</p>
                </div>
                <div style={{ marginTop: 16, textAlign: 'right' }}>
                  <Space>
                    <Button onClick={() => setCurrentStep(1)}>上一步</Button>
                    <Button type="primary" icon={<DownloadOutlined />} onClick={handleExport}>
                      开始导出
                    </Button>
                  </Space>
                </div>
              </div>
            )}
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="快速导出" size="small" style={{ marginBottom: 16 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button block icon={<FileExcelOutlined />}>
                导出客户信息表
              </Button>
              <Button block icon={<FileTextOutlined />}>
                导出性能监控数据
              </Button>
              <Button block icon={<FilePdfOutlined />}>
                导出月度报表
              </Button>
              <Button block icon={<CloudDownloadOutlined />}>
                导出全量数据
              </Button>
            </Space>
          </Card>

          <Card title="导出统计" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>今日导出：</span>
                <span><strong>12</strong> 次</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>本月导出：</span>
                <span><strong>156</strong> 次</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>总数据量：</span>
                <span><strong>2.5GB</strong></span>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DataExportSimple;
