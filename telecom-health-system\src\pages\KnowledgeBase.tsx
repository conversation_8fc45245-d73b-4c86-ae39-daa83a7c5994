import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Input,
  Tree,
  List,
  Tag,
  Space,
  Modal,
  Form,
  Select,
  Alert,
  Statistic,
  Typography,
  Divider,
  message
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  BookOutlined,
  FileTextOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  FolderOutlined,
  StarOutlined,
  DownloadOutlined
} from '@ant-design/icons';

const { Search } = Input;
const { Option } = Select;
const { Title, Paragraph, Text } = Typography;
const { TextArea } = Input;

const KnowledgeBase: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchKeyword, setSearchKeyword] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedArticle, setSelectedArticle] = useState(null);
  const [form] = Form.useForm();

  const knowledgeStats = {
    totalArticles: 156,
    categories: 12,
    views: 8945,
    downloads: 234
  };

  const categoryTree = [
    {
      title: '网络故障处理',
      key: 'network-issues',
      icon: <FolderOutlined />,
      children: [
        { title: '路由器故障', key: 'router-issues', icon: <FileTextOutlined /> },
        { title: '交换机故障', key: 'switch-issues', icon: <FileTextOutlined /> },
        { title: '网络中断', key: 'network-outage', icon: <FileTextOutlined /> }
      ]
    },
    {
      title: '设备维护',
      key: 'maintenance',
      icon: <FolderOutlined />,
      children: [
        { title: '预防性维护', key: 'preventive', icon: <FileTextOutlined /> },
        { title: '设备升级', key: 'upgrade', icon: <FileTextOutlined /> },
        { title: '配置管理', key: 'config', icon: <FileTextOutlined /> }
      ]
    },
    {
      title: '客户服务',
      key: 'customer-service',
      icon: <FolderOutlined />,
      children: [
        { title: '服务开通', key: 'service-activation', icon: <FileTextOutlined /> },
        { title: '故障报修', key: 'fault-report', icon: <FileTextOutlined /> },
        { title: '投诉处理', key: 'complaint', icon: <FileTextOutlined /> }
      ]
    }
  ];

  const knowledgeArticles = [
    {
      id: 'KB-001',
      title: '核心路由器故障排查指南',
      category: '网络故障处理',
      author: '张工程师',
      createTime: '2024-01-15 10:30',
      updateTime: '2024-01-15 14:20',
      views: 245,
      downloads: 12,
      tags: ['路由器', '故障排查', '网络'],
      summary: '详细介绍核心路由器常见故障的排查方法和解决步骤',
      content: '本文档详细介绍了核心路由器故障排查的标准流程...',
      status: 'published',
      priority: 'high'
    },
    {
      id: 'KB-002',
      title: '客户专线开通标准流程',
      category: '客户服务',
      author: '李技术员',
      createTime: '2024-01-14 16:45',
      updateTime: '2024-01-14 17:30',
      views: 189,
      downloads: 8,
      tags: ['专线', '开通流程', '客户服务'],
      summary: '客户专线业务开通的标准操作流程和注意事项',
      content: '专线开通流程包括需求确认、资源分配、设备配置...',
      status: 'published',
      priority: 'medium'
    },
    {
      id: 'KB-003',
      title: '防火墙安全策略配置手册',
      category: '设备维护',
      author: '王安全专员',
      createTime: '2024-01-13 09:15',
      updateTime: '2024-01-13 11:20',
      views: 156,
      downloads: 15,
      tags: ['防火墙', '安全策略', '配置'],
      summary: '防火墙安全策略的配置方法和最佳实践',
      content: '防火墙安全策略配置需要考虑业务需求和安全要求...',
      status: 'published',
      priority: 'high'
    },
    {
      id: 'KB-004',
      title: '网络性能监控最佳实践',
      category: '设备维护',
      author: '刘运维',
      createTime: '2024-01-12 14:30',
      updateTime: '2024-01-12 16:45',
      views: 203,
      downloads: 9,
      tags: ['性能监控', '最佳实践', '网络'],
      summary: '网络性能监控的工具选择、指标设置和告警配置',
      content: '有效的网络性能监控需要选择合适的监控工具...',
      status: 'draft',
      priority: 'medium'
    }
  ];

  const getStatusColor = (status: string) => {
    const colors = {
      published: 'green',
      draft: 'orange',
      archived: 'default'
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      high: 'red',
      medium: 'orange',
      low: 'green'
    };
    return colors[priority as keyof typeof colors] || 'default';
  };

  const filteredArticles = knowledgeArticles.filter(article => {
    const matchesCategory = selectedCategory === 'all' || article.category === selectedCategory;
    const matchesKeyword = !searchKeyword || 
      article.title.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      article.tags.some(tag => tag.toLowerCase().includes(searchKeyword.toLowerCase()));
    return matchesCategory && matchesKeyword;
  });

  const showArticleDetail = (article: any) => {
    setSelectedArticle(article);
    setDetailModalVisible(true);
  };

  const handleEdit = (article: any) => {
    setSelectedArticle(article);
    form.setFieldsValue(article);
    setIsModalVisible(true);
  };

  const handleDelete = (article: any) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除知识库文章 "${article.title}" 吗？`,
      onOk() {
        message.success('删除成功');
      },
    });
  };

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Alert
            message="知识库管理"
            description="管理技术文档、操作手册和最佳实践，为团队提供知识共享平台"
            type="info"
            showIcon
          />
        </Col>
      </Row>

      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="文章总数"
              value={knowledgeStats.totalArticles}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="分类数量"
              value={knowledgeStats.categories}
              prefix={<FolderOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总浏览量"
              value={knowledgeStats.views}
              prefix={<EyeOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="下载次数"
              value={knowledgeStats.downloads}
              prefix={<DownloadOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col xs={24} lg={6}>
          <Card title="知识分类" size="small">
            <Tree
              showIcon
              defaultExpandAll
              treeData={categoryTree}
              onSelect={(keys) => {
                if (keys.length > 0) {
                  const selectedKey = keys[0] as string;
                  const category = categoryTree.find(cat => 
                    cat.key === selectedKey || 
                    cat.children?.some(child => child.key === selectedKey)
                  );
                  setSelectedCategory(category ? category.title : 'all');
                }
              }}
            />
          </Card>
        </Col>

        <Col xs={24} lg={18}>
          <Card
            title="知识库文章"
            extra={
              <Space>
                <Search
                  placeholder="搜索文章..."
                  allowClear
                  style={{ width: 200 }}
                  onSearch={setSearchKeyword}
                />
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setSelectedArticle(null);
                    form.resetFields();
                    setIsModalVisible(true);
                  }}
                >
                  新建文章
                </Button>
              </Space>
            }
          >
            <List
              itemLayout="vertical"
              dataSource={filteredArticles}
              pagination={{
                pageSize: 5,
                showSizeChanger: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
              }}
              renderItem={(article) => (
                <List.Item
                  key={article.id}
                  actions={[
                    <Button
                      type="text"
                      icon={<EyeOutlined />}
                      onClick={() => showArticleDetail(article)}
                    >
                      查看
                    </Button>,
                    <Button
                      type="text"
                      icon={<EditOutlined />}
                      onClick={() => handleEdit(article)}
                    >
                      编辑
                    </Button>,
                    <Button
                      type="text"
                      icon={<DownloadOutlined />}
                      onClick={() => message.success('下载成功')}
                    >
                      下载
                    </Button>,
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => handleDelete(article)}
                    >
                      删除
                    </Button>
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <div>
                        <Text strong>{article.title}</Text>
                        <div style={{ marginTop: 4 }}>
                          <Tag color={getStatusColor(article.status)}>
                            {article.status === 'published' ? '已发布' : 
                             article.status === 'draft' ? '草稿' : '已归档'}
                          </Tag>
                          <Tag color={getPriorityColor(article.priority)}>
                            {article.priority === 'high' ? '高优先级' : 
                             article.priority === 'medium' ? '中优先级' : '低优先级'}
                          </Tag>
                          {article.tags.map(tag => (
                            <Tag key={tag}>{tag}</Tag>
                          ))}
                        </div>
                      </div>
                    }
                    description={
                      <div>
                        <div>{article.summary}</div>
                        <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
                          作者：{article.author} | 
                          创建时间：{article.createTime} | 
                          浏览：{article.views} | 
                          下载：{article.downloads}
                        </div>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      <Modal
        title={selectedArticle ? '编辑文章' : '新建文章'}
        open={isModalVisible}
        onOk={() => {
          form.validateFields().then(() => {
            message.success(selectedArticle ? '更新成功' : '创建成功');
            setIsModalVisible(false);
          });
        }}
        onCancel={() => setIsModalVisible(false)}
        width={800}
      >
        <Form form={form} layout="vertical">
          <Form.Item name="title" label="文章标题" rules={[{ required: true }]}>
            <Input placeholder="请输入文章标题" />
          </Form.Item>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="category" label="分类" rules={[{ required: true }]}>
                <Select placeholder="请选择分类">
                  <Option value="网络故障处理">网络故障处理</Option>
                  <Option value="设备维护">设备维护</Option>
                  <Option value="客户服务">客户服务</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="priority" label="优先级" rules={[{ required: true }]}>
                <Select placeholder="请选择优先级">
                  <Option value="high">高</Option>
                  <Option value="medium">中</Option>
                  <Option value="low">低</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item name="summary" label="摘要" rules={[{ required: true }]}>
            <TextArea rows={2} placeholder="请输入文章摘要" />
          </Form.Item>
          <Form.Item name="content" label="内容" rules={[{ required: true }]}>
            <TextArea rows={8} placeholder="请输入文章内容" />
          </Form.Item>
          <Form.Item name="tags" label="标签">
            <Input placeholder="请输入标签，用逗号分隔" />
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="文章详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedArticle && (
          <div>
            <Title level={3}>{(selectedArticle as any).title}</Title>
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Text type="secondary">作者：{(selectedArticle as any).author}</Text>
                <Text type="secondary">创建时间：{(selectedArticle as any).createTime}</Text>
                <Text type="secondary">浏览：{(selectedArticle as any).views}</Text>
              </Space>
            </div>
            <div style={{ marginBottom: 16 }}>
              {(selectedArticle as any).tags.map((tag: string) => (
                <Tag key={tag}>{tag}</Tag>
              ))}
            </div>
            <Divider />
            <Paragraph>{(selectedArticle as any).summary}</Paragraph>
            <Paragraph>{(selectedArticle as any).content}</Paragraph>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default KnowledgeBase;
