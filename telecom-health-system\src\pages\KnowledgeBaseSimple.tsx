import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Input,
  List,
  Tag,
  Space,
  Modal,
  Form,
  Select,
  Alert,
  Statistic,
  Typography,
  message
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  BookOutlined,
  FileTextOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  StarOutlined,
  DownloadOutlined
} from '@ant-design/icons';

const { Search } = Input;
const { Option } = Select;
const { Title, Paragraph } = Typography;
const { TextArea } = Input;

const KnowledgeBaseSimple: React.FC = () => {
  const [searchKeyword, setSearchKeyword] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedArticle, setSelectedArticle] = useState(null);
  const [form] = Form.useForm();

  const knowledgeStats = {
    totalArticles: 156,
    categories: 12,
    views: 8945,
    downloads: 234
  };

  const knowledgeArticles = [
    {
      id: 'KB-001',
      title: '核心路由器故障排查指南',
      category: '网络故障处理',
      author: '张工程师',
      createTime: '2024-01-15 10:30',
      views: 245,
      downloads: 12,
      tags: ['路由器', '故障排查', '网络'],
      summary: '详细介绍核心路由器常见故障的排查方法和解决步骤',
      content: '本文档详细介绍了核心路由器故障排查的标准流程，包括硬件检查、软件诊断、日志分析等关键步骤。',
      status: 'published'
    },
    {
      id: 'KB-002',
      title: '客户专线开通标准流程',
      category: '客户服务',
      author: '李技术员',
      createTime: '2024-01-14 16:45',
      views: 189,
      downloads: 8,
      tags: ['专线', '开通流程', '客户服务'],
      summary: '客户专线业务开通的标准操作流程和注意事项',
      content: '专线开通流程包括需求确认、资源分配、设备配置、测试验收等环节，确保服务质量。',
      status: 'published'
    },
    {
      id: 'KB-003',
      title: '防火墙安全策略配置手册',
      category: '设备维护',
      author: '王安全专员',
      createTime: '2024-01-13 09:15',
      views: 156,
      downloads: 15,
      tags: ['防火墙', '安全策略', '配置'],
      summary: '防火墙安全策略的配置方法和最佳实践',
      content: '防火墙安全策略配置需要考虑业务需求和安全要求，本文提供详细的配置指导。',
      status: 'published'
    },
    {
      id: 'KB-004',
      title: '网络性能监控最佳实践',
      category: '设备维护',
      author: '刘运维',
      createTime: '2024-01-12 14:30',
      views: 203,
      downloads: 9,
      tags: ['性能监控', '最佳实践', '网络'],
      summary: '网络性能监控的工具选择、指标设置和告警配置',
      content: '有效的网络性能监控需要选择合适的监控工具，设置关键性能指标，配置及时告警。',
      status: 'draft'
    }
  ];

  const getStatusColor = (status: string) => {
    return status === 'published' ? 'green' : 'orange';
  };

  const filteredArticles = knowledgeArticles.filter(article => {
    return !searchKeyword || 
      article.title.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      article.tags.some(tag => tag.toLowerCase().includes(searchKeyword.toLowerCase()));
  });

  const showArticleDetail = (article: any) => {
    setSelectedArticle(article);
    setDetailModalVisible(true);
  };

  const handleEdit = (article: any) => {
    setSelectedArticle(article);
    form.setFieldsValue(article);
    setIsModalVisible(true);
  };

  const handleDelete = (article: any) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除知识库文章 "${article.title}" 吗？`,
      onOk() {
        message.success('删除成功');
      },
    });
  };

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Alert
            message="知识库管理"
            description="管理技术文档、操作手册和最佳实践，为团队提供知识共享平台"
            type="info"
            showIcon
          />
        </Col>
      </Row>

      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="文章总数"
              value={knowledgeStats.totalArticles}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="分类数量"
              value={knowledgeStats.categories}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总浏览量"
              value={knowledgeStats.views}
              prefix={<EyeOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="下载次数"
              value={knowledgeStats.downloads}
              prefix={<DownloadOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      <Card
        title="知识库文章"
        extra={
          <Space>
            <Search
              placeholder="搜索文章..."
              allowClear
              style={{ width: 200 }}
              onSearch={setSearchKeyword}
            />
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setSelectedArticle(null);
                form.resetFields();
                setIsModalVisible(true);
              }}
            >
              新建文章
            </Button>
          </Space>
        }
      >
        <List
          itemLayout="vertical"
          dataSource={filteredArticles}
          pagination={{
            pageSize: 5,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          renderItem={(article) => (
            <List.Item
              key={article.id}
              actions={[
                <Button
                  type="text"
                  icon={<EyeOutlined />}
                  onClick={() => showArticleDetail(article)}
                >
                  查看
                </Button>,
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={() => handleEdit(article)}
                >
                  编辑
                </Button>,
                <Button
                  type="text"
                  icon={<DownloadOutlined />}
                  onClick={() => message.success('下载成功')}
                >
                  下载
                </Button>,
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => handleDelete(article)}
                >
                  删除
                </Button>
              ]}
            >
              <List.Item.Meta
                title={
                  <div>
                    <span style={{ fontWeight: 'bold' }}>{article.title}</span>
                    <div style={{ marginTop: 4 }}>
                      <Tag color={getStatusColor(article.status)}>
                        {article.status === 'published' ? '已发布' : '草稿'}
                      </Tag>
                      {article.tags.map(tag => (
                        <Tag key={tag}>{tag}</Tag>
                      ))}
                    </div>
                  </div>
                }
                description={
                  <div>
                    <div>{article.summary}</div>
                    <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
                      作者：{article.author} | 
                      创建时间：{article.createTime} | 
                      浏览：{article.views} | 
                      下载：{article.downloads}
                    </div>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </Card>

      <Modal
        title={selectedArticle ? '编辑文章' : '新建文章'}
        open={isModalVisible}
        onOk={() => {
          form.validateFields().then(() => {
            message.success(selectedArticle ? '更新成功' : '创建成功');
            setIsModalVisible(false);
          });
        }}
        onCancel={() => setIsModalVisible(false)}
        width={800}
      >
        <Form form={form} layout="vertical">
          <Form.Item name="title" label="文章标题" rules={[{ required: true }]}>
            <Input placeholder="请输入文章标题" />
          </Form.Item>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="category" label="分类" rules={[{ required: true }]}>
                <Select placeholder="请选择分类">
                  <Option value="网络故障处理">网络故障处理</Option>
                  <Option value="设备维护">设备维护</Option>
                  <Option value="客户服务">客户服务</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="status" label="状态" rules={[{ required: true }]}>
                <Select placeholder="请选择状态">
                  <Option value="published">已发布</Option>
                  <Option value="draft">草稿</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item name="summary" label="摘要" rules={[{ required: true }]}>
            <TextArea rows={2} placeholder="请输入文章摘要" />
          </Form.Item>
          <Form.Item name="content" label="内容" rules={[{ required: true }]}>
            <TextArea rows={6} placeholder="请输入文章内容" />
          </Form.Item>
          <Form.Item name="tags" label="标签">
            <Input placeholder="请输入标签，用逗号分隔" />
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="文章详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedArticle && (
          <div>
            <Title level={3}>{(selectedArticle as any).title}</Title>
            <div style={{ marginBottom: 16 }}>
              <Space>
                <span>作者：{(selectedArticle as any).author}</span>
                <span>创建时间：{(selectedArticle as any).createTime}</span>
                <span>浏览：{(selectedArticle as any).views}</span>
              </Space>
            </div>
            <div style={{ marginBottom: 16 }}>
              {(selectedArticle as any).tags.map((tag: string) => (
                <Tag key={tag}>{tag}</Tag>
              ))}
            </div>
            <Paragraph>{(selectedArticle as any).summary}</Paragraph>
            <Paragraph>{(selectedArticle as any).content}</Paragraph>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default KnowledgeBaseSimple;
