import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Descriptions,
  Table,
  Tag,
  Progress,
  Statistic,
  Timeline,
  Tabs,
  Button,
  Space,
  Avatar,
  Alert,
  Divider
} from 'antd';
import {
  UserOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  LineChartOutlined,
  WifiOutlined
} from '@ant-design/icons';

const { TabPane } = Tabs;

const CustomerProfile: React.FC = () => {
  const [selectedCustomer, setSelectedCustomer] = useState('CUS001');

  // 模拟客户详细信息
  const customerDetail = {
    id: 'CUS001',
    name: '中国移动北京分公司',
    type: '运营商',
    level: 'VIP',
    contact: '张经理',
    phone: '138****8888',
    email: '<EMAIL>',
    address: '北京市朝阳区建国门外大街9号',
    contractDate: '2023-01-15',
    contractExpiry: '2025-01-15',
    status: 'active',
    healthScore: 98,
    totalServices: 156,
    activeServices: 152,
    monthlyRevenue: 2850000,
    lastActivity: '2024-01-15 14:30:25'
  };

  // 服务列表
  const serviceData = [
    {
      key: '1',
      serviceName: '5G核心网服务',
      serviceType: '核心网',
      status: 'running',
      healthScore: 99,
      bandwidth: '10Gbps',
      latency: '5ms',
      uptime: '99.99%',
      lastCheck: '2024-01-15 14:30'
    },
    {
      key: '2',
      serviceName: '数据中心互联',
      serviceType: '专线',
      status: 'running',
      healthScore: 95,
      bandwidth: '100Gbps',
      latency: '8ms',
      uptime: '99.95%',
      lastCheck: '2024-01-15 14:25'
    },
    {
      key: '3',
      serviceName: '云网融合服务',
      serviceType: '云服务',
      status: 'warning',
      healthScore: 85,
      bandwidth: '50Gbps',
      latency: '12ms',
      uptime: '99.8%',
      lastCheck: '2024-01-15 14:20'
    },
    {
      key: '4',
      serviceName: '物联网平台',
      serviceType: 'IoT',
      status: 'maintenance',
      healthScore: 0,
      bandwidth: '1Gbps',
      latency: 'N/A',
      uptime: '0%',
      lastCheck: '2024-01-15 13:00'
    }
  ];

  // 历史事件
  const historyEvents = [
    {
      time: '2024-01-15 14:30',
      type: 'info',
      title: '服务健康检查完成',
      description: '所有核心服务运行正常'
    },
    {
      time: '2024-01-15 13:00',
      type: 'warning',
      title: '物联网平台进入维护',
      description: '预计维护时间2小时'
    },
    {
      time: '2024-01-15 10:15',
      type: 'success',
      title: '带宽扩容完成',
      description: '数据中心互联带宽从50G升级到100G'
    },
    {
      time: '2024-01-14 16:45',
      type: 'error',
      title: '网络延迟异常',
      description: '云网融合服务延迟超过阈值，已自动修复'
    }
  ];

  const getStatusColor = (status: string) => {
    const colors = {
      running: 'green',
      warning: 'orange',
      maintenance: 'blue',
      error: 'red'
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getStatusText = (status: string) => {
    const texts = {
      running: '运行中',
      warning: '告警',
      maintenance: '维护中',
      error: '故障'
    };
    return texts[status as keyof typeof texts] || '未知';
  };

  const serviceColumns = [
    {
      title: '服务名称',
      dataIndex: 'serviceName',
      key: 'serviceName',
    },
    {
      title: '服务类型',
      dataIndex: 'serviceType',
      key: 'serviceType',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '健康评分',
      dataIndex: 'healthScore',
      key: 'healthScore',
      render: (score: number) => (
        <Progress
          percent={score}
          size="small"
          strokeColor={score >= 95 ? '#52c41a' : score >= 80 ? '#1890ff' : '#faad14'}
        />
      ),
    },
    {
      title: '带宽',
      dataIndex: 'bandwidth',
      key: 'bandwidth',
    },
    {
      title: '延迟',
      dataIndex: 'latency',
      key: 'latency',
    },
    {
      title: '可用性',
      dataIndex: 'uptime',
      key: 'uptime',
    },
    {
      title: '最后检查',
      dataIndex: 'lastCheck',
      key: 'lastCheck',
    }
  ];

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Alert
            message="客户档案详情"
            description="查看客户的详细信息、服务状态和历史记录"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
        </Col>
      </Row>

      <Row gutter={16} style={{ marginBottom: 24 }}>
        {/* 客户基本信息 */}
        <Col xs={24} lg={8}>
          <Card title="客户基本信息" extra={<Button type="link">编辑</Button>}>
            <div style={{ textAlign: 'center', marginBottom: 16 }}>
              <Avatar size={64} icon={<UserOutlined />} />
              <h3 style={{ marginTop: 8, marginBottom: 4 }}>{customerDetail.name}</h3>
              <Tag color="gold">{customerDetail.level}</Tag>
            </div>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="客户ID">{customerDetail.id}</Descriptions.Item>
              <Descriptions.Item label="客户类型">{customerDetail.type}</Descriptions.Item>
              <Descriptions.Item label="联系人">{customerDetail.contact}</Descriptions.Item>
              <Descriptions.Item label="联系电话">{customerDetail.phone}</Descriptions.Item>
              <Descriptions.Item label="邮箱">{customerDetail.email}</Descriptions.Item>
              <Descriptions.Item label="地址">{customerDetail.address}</Descriptions.Item>
              <Descriptions.Item label="合同期限">
                {customerDetail.contractDate} 至 {customerDetail.contractExpiry}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        {/* 服务统计 */}
        <Col xs={24} lg={16}>
          <Card title="服务概览">
            <Row gutter={16}>
              <Col xs={12} sm={6}>
                <Statistic
                  title="健康评分"
                  value={customerDetail.healthScore}
                  suffix="%"
                  valueStyle={{ color: '#52c41a' }}
                  prefix={<LineChartOutlined />}
                />
              </Col>
              <Col xs={12} sm={6}>
                <Statistic
                  title="总服务数"
                  value={customerDetail.totalServices}
                  prefix={<WifiOutlined />}
                />
              </Col>
              <Col xs={12} sm={6}>
                <Statistic
                  title="运行服务"
                  value={customerDetail.activeServices}
                  valueStyle={{ color: '#1890ff' }}
                  prefix={<CheckCircleOutlined />}
                />
              </Col>
              <Col xs={12} sm={6}>
                <Statistic
                  title="月收入"
                  value={customerDetail.monthlyRevenue}
                  precision={0}
                  prefix="¥"
                  valueStyle={{ color: '#cf1322' }}
                />
              </Col>
            </Row>
            <Divider />
            <Progress
              percent={customerDetail.healthScore}
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
              format={(percent) => `整体健康度 ${percent}%`}
            />
          </Card>
        </Col>
      </Row>

      {/* 详细信息标签页 */}
      <Card>
        <Tabs defaultActiveKey="services">
          <TabPane tab="服务列表" key="services">
            <Table
              columns={serviceColumns}
              dataSource={serviceData}
              pagination={false}
              scroll={{ x: 800 }}
            />
          </TabPane>
          
          <TabPane tab="历史记录" key="history">
            <Timeline>
              {historyEvents.map((event, index) => (
                <Timeline.Item
                  key={index}
                  color={
                    event.type === 'success' ? 'green' :
                    event.type === 'warning' ? 'orange' :
                    event.type === 'error' ? 'red' : 'blue'
                  }
                  dot={
                    event.type === 'success' ? <CheckCircleOutlined /> :
                    event.type === 'warning' ? <ExclamationCircleOutlined /> :
                    event.type === 'error' ? <ExclamationCircleOutlined /> :
                    <ClockCircleOutlined />
                  }
                >
                  <div>
                    <h4>{event.title}</h4>
                    <p>{event.description}</p>
                    <small style={{ color: '#999' }}>{event.time}</small>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </TabPane>

          <TabPane tab="SLA报告" key="sla">
            <Row gutter={16}>
              <Col xs={24} sm={8}>
                <Card>
                  <Statistic
                    title="本月可用性"
                    value={99.95}
                    precision={2}
                    suffix="%"
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={8}>
                <Card>
                  <Statistic
                    title="平均响应时间"
                    value={8.5}
                    precision={1}
                    suffix="ms"
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={8}>
                <Card>
                  <Statistic
                    title="故障恢复时间"
                    value={15}
                    suffix="分钟"
                    valueStyle={{ color: '#faad14' }}
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default CustomerProfile;
