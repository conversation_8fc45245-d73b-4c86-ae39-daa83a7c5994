import React from 'react';
import { Card, Row, Col, Statistic, Alert, Table, Tag, Button } from 'antd';
import { TeamOutlined, SafetyOutlined, UserOutlined, CrownOutlined } from '@ant-design/icons';

const PermissionTest: React.FC = () => {
  const rolesData = [
    {
      key: '1',
      name: '超级管理员',
      code: 'super_admin',
      description: '拥有系统所有权限',
      userCount: 2,
      status: 'active'
    },
    {
      key: '2',
      name: '系统管理员',
      code: 'admin',
      description: '管理用户和基本系统配置',
      userCount: 3,
      status: 'active'
    },
    {
      key: '3',
      name: '运维工程师',
      code: 'operator',
      description: '网络监控和设备维护权限',
      userCount: 15,
      status: 'active'
    }
  ];

  const columns = [
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '角色代码',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '用户数量',
      dataIndex: 'userCount',
      key: 'userCount',
      render: (count: number) => <Tag color="blue">{count} 人</Tag>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: () => (
        <div>
          <Button type="text">编辑</Button>
          <Button type="text">权限</Button>
        </div>
      ),
    }
  ];

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Alert
            message="权限管理"
            description="管理系统角色和权限，控制用户对系统功能的访问权限"
            type="info"
            showIcon
          />
        </Col>
      </Row>

      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="角色总数"
              value={8}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="权限总数"
              value={45}
              prefix={<SafetyOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="活跃用户"
              value={38}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="系统模块"
              value={12}
              prefix={<CrownOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      <Card title="角色管理">
        <Table
          columns={columns}
          dataSource={rolesData}
          pagination={false}
        />
      </Card>
    </div>
  );
};

export default PermissionTest;
