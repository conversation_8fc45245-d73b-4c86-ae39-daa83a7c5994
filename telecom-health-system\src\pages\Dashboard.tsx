import React from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Progress,
  Table,
  Tag,
  List,
  Avatar,
  Typography,
  Space,
  Alert,
  Timeline
} from 'antd';
import {
  UserOutlined,
  WifiOutlined,
  AlertOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  SignalFilled
} from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';

const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const { user } = useAuth();

  // 模拟数据
  const networkStats = {
    totalCustomers: 12580,
    activeConnections: 11234,
    networkHealth: 95.8,
    alertCount: 23
  };

  const recentAlerts = [
    {
      id: 1,
      type: 'warning',
      message: '基站 BTS-001 信号强度下降',
      time: '2分钟前',
      status: 'pending'
    },
    {
      id: 2,
      type: 'error',
      message: '核心网设备 CN-005 连接异常',
      time: '15分钟前',
      status: 'processing'
    },
    {
      id: 3,
      type: 'info',
      message: '定期维护：光纤链路检查完成',
      time: '1小时前',
      status: 'completed'
    }
  ];

  const customerHealthData = [
    {
      key: '1',
      customer: '中国移动北京分公司',
      healthScore: 98,
      status: 'excellent',
      lastCheck: '2024-01-15 14:30',
      issues: 0
    },
    {
      key: '2',
      customer: '联通上海数据中心',
      healthScore: 85,
      status: 'good',
      lastCheck: '2024-01-15 14:25',
      issues: 2
    },
    {
      key: '3',
      customer: '电信广州核心网',
      healthScore: 72,
      status: 'warning',
      lastCheck: '2024-01-15 14:20',
      issues: 5
    },
    {
      key: '4',
      customer: '移动深圳基站群',
      healthScore: 45,
      status: 'critical',
      lastCheck: '2024-01-15 14:15',
      issues: 12
    }
  ];

  const getStatusColor = (status: string) => {
    const colors = {
      excellent: 'green',
      good: 'blue',
      warning: 'orange',
      critical: 'red'
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getStatusText = (status: string) => {
    const texts = {
      excellent: '优秀',
      good: '良好',
      warning: '警告',
      critical: '严重'
    };
    return texts[status as keyof typeof texts] || '未知';
  };

  const columns = [
    {
      title: '客户名称',
      dataIndex: 'customer',
      key: 'customer',
    },
    {
      title: '健康评分',
      dataIndex: 'healthScore',
      key: 'healthScore',
      render: (score: number) => (
        <Progress
          percent={score}
          size="small"
          strokeColor={score >= 90 ? '#52c41a' : score >= 70 ? '#1890ff' : score >= 50 ? '#faad14' : '#ff4d4f'}
        />
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '最后检查',
      dataIndex: 'lastCheck',
      key: 'lastCheck',
    },
    {
      title: '问题数量',
      dataIndex: 'issues',
      key: 'issues',
      render: (issues: number) => (
        <Text type={issues > 0 ? 'danger' : 'success'}>
          {issues}
        </Text>
      ),
    },
  ];

  const timelineData = [
    {
      color: 'green',
      children: (
        <div>
          <Text strong>系统维护完成</Text>
          <br />
          <Text type="secondary">核心网设备升级成功</Text>
          <br />
          <Text type="secondary">14:30</Text>
        </div>
      ),
    },
    {
      color: 'blue',
      children: (
        <div>
          <Text strong>性能优化</Text>
          <br />
          <Text type="secondary">网络吞吐量提升15%</Text>
          <br />
          <Text type="secondary">13:45</Text>
        </div>
      ),
    },
    {
      color: 'orange',
      children: (
        <div>
          <Text strong>告警处理</Text>
          <br />
          <Text type="secondary">基站信号异常已修复</Text>
          <br />
          <Text type="secondary">12:20</Text>
        </div>
      ),
    },
    {
      color: 'red',
      children: (
        <div>
          <Text strong>故障报告</Text>
          <br />
          <Text type="secondary">光纤链路中断检测</Text>
          <br />
          <Text type="secondary">11:15</Text>
        </div>
      ),
    },
  ];

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Alert
            message={`欢迎回来，${user?.username}！`}
            description={`当前时间：${new Date().toLocaleString('zh-CN')} | 部门：${user?.department} | 角色：${user?.role}`}
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
        </Col>
      </Row>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总客户数"
              value={networkStats.totalCustomers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
              suffix={
                <Space>
                  <ArrowUpOutlined />
                  <Text type="success">12%</Text>
                </Space>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="活跃连接"
              value={networkStats.activeConnections}
              prefix={<WifiOutlined />}
              valueStyle={{ color: '#1890ff' }}
              suffix={
                <Space>
                  <ArrowUpOutlined />
                  <Text type="success">8%</Text>
                </Space>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="网络健康度"
              value={networkStats.networkHealth}
              prefix={<SignalFilled />}
              suffix="%"
              valueStyle={{ color: '#52c41a' }}
              precision={1}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="待处理告警"
              value={networkStats.alertCount}
              prefix={<AlertOutlined />}
              valueStyle={{ color: '#cf1322' }}
              suffix={
                <Space>
                  <ArrowDownOutlined />
                  <Text type="danger">5</Text>
                </Space>
              }
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={16} style={{ marginBottom: 24 }}>
        {/* 客户健康状况表格 */}
        <Col xs={24} lg={16}>
          <Card title="客户健康状况" extra={<a href="#">查看全部</a>}>
            <Table
              columns={columns}
              dataSource={customerHealthData}
              pagination={false}
              size="small"
            />
          </Card>
        </Col>

        {/* 最近告警 */}
        <Col xs={24} lg={8}>
          <Card title="最近告警" extra={<a href="#">查看全部</a>}>
            <List
              itemLayout="horizontal"
              dataSource={recentAlerts}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        icon={
                          item.type === 'error' ? <ExclamationCircleOutlined /> :
                          item.type === 'warning' ? <AlertOutlined /> :
                          <CheckCircleOutlined />
                        }
                        style={{
                          backgroundColor:
                            item.type === 'error' ? '#ff4d4f' :
                            item.type === 'warning' ? '#faad14' :
                            '#52c41a'
                        }}
                      />
                    }
                    title={<Text style={{ fontSize: 12 }}>{item.message}</Text>}
                    description={
                      <Space>
                        <ClockCircleOutlined />
                        <Text type="secondary" style={{ fontSize: 11 }}>
                          {item.time}
                        </Text>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        {/* 网络性能趋势 */}
        <Col xs={24} lg={12}>
          <Card title="网络性能趋势">
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <Progress
                type="circle"
                percent={95.8}
                format={(percent) => `${percent}%`}
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
              />
              <div style={{ marginTop: 16 }}>
                <Title level={4}>网络整体健康度</Title>
                <Text type="secondary">过去24小时平均值</Text>
              </div>
            </div>
          </Card>
        </Col>

        {/* 操作日志 */}
        <Col xs={24} lg={12}>
          <Card title="操作日志" extra={<a href="#">查看全部</a>}>
            <Timeline items={timelineData} />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
