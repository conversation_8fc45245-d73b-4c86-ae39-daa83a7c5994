import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Progress,
  Table,
  Tag,
  Space,
  Button,
  Select,
  DatePicker,
  Alert,
  Tooltip,
  Badge
} from 'antd';
import {
  WifiOutlined,
  SignalFilled,
  ThunderboltOutlined,
  DatabaseOutlined,
  ReloadOutlined,
  WarningOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { RangePicker } = DatePicker;

const NetworkStatus: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [selectedRegion, setSelectedRegion] = useState('all');
  const [autoRefresh, setAutoRefresh] = useState(true);

  // 模拟实时数据更新
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        // 这里可以添加实时数据更新逻辑
        console.log('Refreshing network data...');
      }, 30000); // 30秒刷新一次

      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  // 模拟网络统计数据
  const networkStats = {
    totalNodes: 1256,
    activeNodes: 1198,
    bandwidth: 85.6,
    latency: 12.3,
    packetLoss: 0.02,
    uptime: 99.95
  };

  // 模拟网络节点数据
  const networkNodes = [
    {
      key: '1',
      nodeId: 'BTS-001',
      name: '北京基站-001',
      type: '基站',
      region: '北京',
      status: 'online',
      health: 98,
      bandwidth: '95%',
      latency: '8ms',
      lastUpdate: '2024-01-15 14:30:25'
    },
    {
      key: '2',
      nodeId: 'CN-005',
      name: '上海核心网-005',
      type: '核心网',
      region: '上海',
      status: 'warning',
      health: 75,
      bandwidth: '78%',
      latency: '15ms',
      lastUpdate: '2024-01-15 14:30:20'
    },
    {
      key: '3',
      nodeId: 'DC-012',
      name: '广州数据中心-012',
      type: '数据中心',
      region: '广州',
      status: 'online',
      health: 92,
      bandwidth: '88%',
      latency: '10ms',
      lastUpdate: '2024-01-15 14:30:18'
    },
    {
      key: '4',
      nodeId: 'BTS-089',
      name: '深圳基站-089',
      type: '基站',
      region: '深圳',
      status: 'offline',
      health: 0,
      bandwidth: '0%',
      latency: 'N/A',
      lastUpdate: '2024-01-15 13:45:12'
    },
    {
      key: '5',
      nodeId: 'RT-023',
      name: '杭州路由器-023',
      type: '路由器',
      region: '杭州',
      status: 'online',
      health: 88,
      bandwidth: '82%',
      latency: '12ms',
      lastUpdate: '2024-01-15 14:30:15'
    }
  ];

  const getStatusColor = (status: string) => {
    const colors = {
      online: 'green',
      warning: 'orange',
      offline: 'red',
      maintenance: 'blue'
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getStatusText = (status: string) => {
    const texts = {
      online: '在线',
      warning: '警告',
      offline: '离线',
      maintenance: '维护'
    };
    return texts[status as keyof typeof texts] || '未知';
  };

  const getHealthColor = (health: number) => {
    if (health >= 90) return '#52c41a';
    if (health >= 70) return '#1890ff';
    if (health >= 50) return '#faad14';
    return '#ff4d4f';
  };

  const columns = [
    {
      title: '节点ID',
      dataIndex: 'nodeId',
      key: 'nodeId',
      width: 100,
    },
    {
      title: '节点名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <Space>
          <Badge
            status={record.status === 'online' ? 'success' : 
                   record.status === 'warning' ? 'warning' : 'error'}
          />
          {text}
        </Space>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
    },
    {
      title: '区域',
      dataIndex: 'region',
      key: 'region',
      width: 80,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '健康度',
      dataIndex: 'health',
      key: 'health',
      width: 120,
      render: (health: number) => (
        <Progress
          percent={health}
          size="small"
          strokeColor={getHealthColor(health)}
          format={(percent) => `${percent}%`}
        />
      ),
    },
    {
      title: '带宽使用',
      dataIndex: 'bandwidth',
      key: 'bandwidth',
      width: 100,
    },
    {
      title: '延迟',
      dataIndex: 'latency',
      key: 'latency',
      width: 80,
    },
    {
      title: '最后更新',
      dataIndex: 'lastUpdate',
      key: 'lastUpdate',
      width: 150,
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record: any) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button type="text" size="small">
              详情
            </Button>
          </Tooltip>
          <Tooltip title="重启节点">
            <Button type="text" size="small" danger>
              重启
            </Button>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  return (
    <div>
      {/* 告警信息 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Alert
            message="网络状态监控"
            description="实时监控网络节点状态，自动刷新间隔30秒"
            type="info"
            showIcon
            action={
              <Space>
                <Button
                  size="small"
                  type={autoRefresh ? 'primary' : 'default'}
                  onClick={() => setAutoRefresh(!autoRefresh)}
                >
                  {autoRefresh ? '停止自动刷新' : '开启自动刷新'}
                </Button>
                <Button
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={loading}
                >
                  手动刷新
                </Button>
              </Space>
            }
          />
        </Col>
      </Row>

      {/* 网络统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={4}>
          <Card>
            <Statistic
              title="总节点数"
              value={networkStats.totalNodes}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={4}>
          <Card>
            <Statistic
              title="在线节点"
              value={networkStats.activeNodes}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
              suffix={`/ ${networkStats.totalNodes}`}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={4}>
          <Card>
            <Statistic
              title="平均带宽使用"
              value={networkStats.bandwidth}
              prefix={<WifiOutlined />}
              suffix="%"
              precision={1}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={4}>
          <Card>
            <Statistic
              title="平均延迟"
              value={networkStats.latency}
              prefix={<ThunderboltOutlined />}
              suffix="ms"
              precision={1}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={4}>
          <Card>
            <Statistic
              title="丢包率"
              value={networkStats.packetLoss}
              prefix={<WarningOutlined />}
              suffix="%"
              precision={2}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={4}>
          <Card>
            <Statistic
              title="系统可用性"
              value={networkStats.uptime}
              prefix={<SignalFilled />}
              suffix="%"
              precision={2}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 网络节点列表 */}
      <Card
        title="网络节点状态"
        extra={
          <Space>
            <Select
              value={selectedRegion}
              onChange={setSelectedRegion}
              style={{ width: 120 }}
            >
              <Option value="all">全部区域</Option>
              <Option value="北京">北京</Option>
              <Option value="上海">上海</Option>
              <Option value="广州">广州</Option>
              <Option value="深圳">深圳</Option>
              <Option value="杭州">杭州</Option>
            </Select>
            <RangePicker size="small" />
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={networkNodes.filter(node => 
            selectedRegion === 'all' || node.region === selectedRegion
          )}
          loading={loading}
          pagination={{
            total: networkNodes.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  );
};

export default NetworkStatus;
