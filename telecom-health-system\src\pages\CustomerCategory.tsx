import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Table,
  Tag,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  message,
  Statistic,
  Progress,
  Tooltip,
  Popconfirm
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  CrownOutlined,
  StarOutlined,
  TeamOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { TextArea } = Input;

const CustomerCategory: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [form] = Form.useForm();

  // 模拟客户分类数据
  const [categories, setCategories] = useState([
    {
      key: '1',
      id: 'CAT001',
      name: 'VIP客户',
      level: 1,
      description: '年收入超过1000万的重要客户',
      customerCount: 25,
      minRevenue: 10000000,
      maxRevenue: null,
      privileges: ['专属客户经理', '7x24技术支持', '优先故障处理', 'SLA 99.99%'],
      color: 'gold',
      icon: 'crown',
      createTime: '2023-01-15'
    },
    {
      key: '2',
      id: 'CAT002',
      name: '企业客户',
      level: 2,
      description: '年收入100万-1000万的企业客户',
      customerCount: 156,
      minRevenue: 1000000,
      maxRevenue: 10000000,
      privileges: ['专属技术支持', '工作时间支持', 'SLA 99.9%'],
      color: 'blue',
      icon: 'star',
      createTime: '2023-01-15'
    },
    {
      key: '3',
      id: 'CAT003',
      name: '标准客户',
      level: 3,
      description: '年收入10万-100万的标准客户',
      customerCount: 892,
      minRevenue: 100000,
      maxRevenue: 1000000,
      privileges: ['在线技术支持', 'SLA 99.5%'],
      color: 'green',
      icon: 'user',
      createTime: '2023-01-15'
    },
    {
      key: '4',
      id: 'CAT004',
      name: '基础客户',
      level: 4,
      description: '年收入低于10万的基础客户',
      customerCount: 2341,
      minRevenue: 0,
      maxRevenue: 100000,
      privileges: ['基础技术支持', 'SLA 99%'],
      color: 'default',
      icon: 'team',
      createTime: '2023-01-15'
    }
  ]);

  const getIcon = (iconType: string) => {
    const icons = {
      crown: <CrownOutlined />,
      star: <StarOutlined />,
      user: <UserOutlined />,
      team: <TeamOutlined />
    };
    return icons[iconType as keyof typeof icons] || <UserOutlined />;
  };

  const formatRevenue = (amount: number | null) => {
    if (amount === null) return '无上限';
    if (amount >= 10000000) return `${(amount / 10000000).toFixed(0)}千万`;
    if (amount >= 1000000) return `${(amount / 1000000).toFixed(0)}百万`;
    if (amount >= 10000) return `${(amount / 10000).toFixed(0)}万`;
    return amount.toString();
  };

  const columns = [
    {
      title: '分类名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <Space>
          {getIcon(record.icon)}
          <span>{text}</span>
          <Tag color={record.color}>等级{record.level}</Tag>
        </Space>
      ),
    },
    {
      title: '客户数量',
      dataIndex: 'customerCount',
      key: 'customerCount',
      render: (count: number) => (
        <Statistic value={count} suffix="个" />
      ),
    },
    {
      title: '收入范围',
      key: 'revenueRange',
      render: (_, record: any) => (
        <span>
          {formatRevenue(record.minRevenue)} - {formatRevenue(record.maxRevenue)}
        </span>
      ),
    },
    {
      title: '客户占比',
      key: 'percentage',
      render: (_, record: any) => {
        const total = categories.reduce((sum, cat) => sum + cat.customerCount, 0);
        const percentage = (record.customerCount / total * 100).toFixed(1);
        return (
          <Progress
            percent={parseFloat(percentage)}
            size="small"
            format={(percent) => `${percent}%`}
          />
        );
      },
    },
    {
      title: '特权服务',
      dataIndex: 'privileges',
      key: 'privileges',
      render: (privileges: string[]) => (
        <div>
          {privileges.slice(0, 2).map((privilege, index) => (
            <Tag key={index} style={{ marginBottom: 4 }}>
              {privilege}
            </Tag>
          ))}
          {privileges.length > 2 && (
            <Tooltip title={privileges.slice(2).join(', ')}>
              <Tag>+{privileges.length - 2}项</Tag>
            </Tooltip>
          )}
        </div>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record: any) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个分类吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setEditingCategory(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (record: any) => {
    setEditingCategory(record);
    form.setFieldsValue({
      ...record,
      privileges: record.privileges.join('\n')
    });
    setIsModalVisible(true);
  };

  const handleDelete = (record: any) => {
    setCategories(categories.filter(item => item.key !== record.key));
    message.success('删除成功');
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      const privileges = values.privileges.split('\n').filter((p: string) => p.trim());
      
      if (editingCategory) {
        // 编辑
        setCategories(categories.map(item => 
          item.key === (editingCategory as any).key 
            ? { ...item, ...values, privileges }
            : item
        ));
        message.success('更新成功');
      } else {
        // 新增
        const newCategory = {
          key: Date.now().toString(),
          id: `CAT${String(categories.length + 1).padStart(3, '0')}`,
          ...values,
          privileges,
          customerCount: 0,
          createTime: new Date().toISOString().split('T')[0]
        };
        setCategories([...categories, newCategory]);
        message.success('添加成功');
      }
      setIsModalVisible(false);
    });
  };

  // 统计数据
  const totalCustomers = categories.reduce((sum, cat) => sum + cat.customerCount, 0);
  const vipCustomers = categories.filter(cat => cat.level === 1).reduce((sum, cat) => sum + cat.customerCount, 0);
  const enterpriseCustomers = categories.filter(cat => cat.level === 2).reduce((sum, cat) => sum + cat.customerCount, 0);

  return (
    <div>
      {/* 统计概览 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="客户总数"
              value={totalCustomers}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="VIP客户"
              value={vipCustomers}
              prefix={<CrownOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="企业客户"
              value={enterpriseCustomers}
              prefix={<StarOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="分类数量"
              value={categories.length}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 分类管理表格 */}
      <Card
        title="客户分类管理"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            新增分类
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={categories}
          pagination={{
            total: categories.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 新增/编辑分类模态框 */}
      <Modal
        title={editingCategory ? '编辑客户分类' : '新增客户分类'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={() => setIsModalVisible(false)}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            level: 1,
            color: 'blue',
            icon: 'user'
          }}
        >
          <Form.Item
            name="name"
            label="分类名称"
            rules={[{ required: true, message: '请输入分类名称' }]}
          >
            <Input placeholder="请输入分类名称" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="level"
                label="分类等级"
                rules={[{ required: true, message: '请选择分类等级' }]}
              >
                <Select placeholder="请选择分类等级">
                  <Option value={1}>等级1 (最高)</Option>
                  <Option value={2}>等级2</Option>
                  <Option value={3}>等级3</Option>
                  <Option value={4}>等级4</Option>
                  <Option value={5}>等级5 (最低)</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="color"
                label="标签颜色"
                rules={[{ required: true, message: '请选择标签颜色' }]}
              >
                <Select placeholder="请选择标签颜色">
                  <Option value="gold">金色</Option>
                  <Option value="blue">蓝色</Option>
                  <Option value="green">绿色</Option>
                  <Option value="orange">橙色</Option>
                  <Option value="red">红色</Option>
                  <Option value="purple">紫色</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="minRevenue"
                label="最低年收入"
                rules={[{ required: true, message: '请输入最低年收入' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入最低年收入"
                  formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => value!.replace(/¥\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="maxRevenue"
                label="最高年收入"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入最高年收入（可选）"
                  formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => value!.replace(/¥\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="icon"
            label="图标类型"
            rules={[{ required: true, message: '请选择图标类型' }]}
          >
            <Select placeholder="请选择图标类型">
              <Option value="crown">皇冠 (VIP)</Option>
              <Option value="star">星星 (企业)</Option>
              <Option value="user">用户 (标准)</Option>
              <Option value="team">团队 (基础)</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="分类描述"
            rules={[{ required: true, message: '请输入分类描述' }]}
          >
            <TextArea
              rows={3}
              placeholder="请输入分类描述"
            />
          </Form.Item>

          <Form.Item
            name="privileges"
            label="特权服务"
            rules={[{ required: true, message: '请输入特权服务' }]}
          >
            <TextArea
              rows={4}
              placeholder="请输入特权服务，每行一项"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CustomerCategory;
