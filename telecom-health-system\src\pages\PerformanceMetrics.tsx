import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Select,
  DatePicker,
  Button,
  Space,
  Alert,
  Progress,
  Tag,
  Tooltip,
  Tabs,
  Switch
} from 'antd';
import {
  ThunderboltOutlined,
  ClockCircleOutlined,
  WifiOutlined,
  DatabaseOutlined,
  Line<PERSON>hartOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ReloadOutlined,
  DownloadOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

const PerformanceMetrics: React.FC = () => {
  const [selectedRegion, setSelectedRegion] = useState('all');
  const [selectedMetric, setSelectedMetric] = useState('all');
  const [realTimeMode, setRealTimeMode] = useState(true);

  // 性能指标概览
  const performanceOverview = {
    avgLatency: 12.5,
    avgThroughput: 8.5,
    avgPacketLoss: 0.02,
    avgJitter: 2.1,
    avgBandwidthUtil: 75.8,
    avgCpuUtil: 65.2,
    avgMemoryUtil: 72.3,
    avgDiskUtil: 45.6
  };

  // 性能指标详细数据
  const performanceData = [
    {
      key: '1',
      region: '北京',
      deviceType: '核心路由器',
      deviceName: 'RT-BJ-001',
      latency: 8.5,
      throughput: 9.2,
      packetLoss: 0.01,
      jitter: 1.8,
      bandwidthUtil: 85.2,
      cpuUtil: 45.3,
      memoryUtil: 67.8,
      diskUtil: 32.1,
      status: 'excellent',
      lastUpdate: '2024-01-15 14:30:25'
    },
    {
      key: '2',
      region: '上海',
      deviceType: '接入交换机',
      deviceName: 'SW-SH-015',
      latency: 15.2,
      throughput: 7.8,
      packetLoss: 0.03,
      jitter: 2.5,
      bandwidthUtil: 78.1,
      cpuUtil: 85.2,
      memoryUtil: 92.1,
      diskUtil: 78.3,
      status: 'warning',
      lastUpdate: '2024-01-15 14:30:20'
    },
    {
      key: '3',
      region: '广州',
      deviceType: '防火墙',
      deviceName: 'FW-GZ-008',
      latency: 10.8,
      throughput: 8.9,
      packetLoss: 0.02,
      jitter: 1.9,
      bandwidthUtil: 68.5,
      cpuUtil: 38.7,
      memoryUtil: 55.2,
      diskUtil: 41.8,
      status: 'good',
      lastUpdate: '2024-01-15 14:30:18'
    },
    {
      key: '4',
      region: '深圳',
      deviceType: '负载均衡器',
      deviceName: 'LB-SZ-003',
      latency: 25.6,
      throughput: 5.2,
      packetLoss: 0.08,
      jitter: 4.2,
      bandwidthUtil: 92.3,
      cpuUtil: 95.8,
      memoryUtil: 98.5,
      diskUtil: 85.2,
      status: 'critical',
      lastUpdate: '2024-01-15 14:30:15'
    },
    {
      key: '5',
      region: '杭州',
      deviceType: '无线AP',
      deviceName: 'AP-HZ-056',
      latency: 18.3,
      throughput: 6.5,
      packetLoss: 0.04,
      jitter: 3.1,
      bandwidthUtil: 62.8,
      cpuUtil: 28.5,
      memoryUtil: 45.3,
      diskUtil: 25.7,
      status: 'good',
      lastUpdate: '2024-01-15 14:30:12'
    }
  ];

  // 性能阈值配置
  const thresholds = {
    latency: { excellent: 10, good: 20, warning: 50 },
    throughput: { excellent: 8, good: 5, warning: 2 },
    packetLoss: { excellent: 0.01, good: 0.05, warning: 0.1 },
    jitter: { excellent: 2, good: 5, warning: 10 },
    utilization: { excellent: 70, good: 85, warning: 95 }
  };

  const getPerformanceStatus = (value: number, metric: string) => {
    const threshold = thresholds[metric as keyof typeof thresholds];
    if (!threshold) return 'good';

    if (metric === 'throughput') {
      if (value >= threshold.excellent) return 'excellent';
      if (value >= threshold.good) return 'good';
      if (value >= threshold.warning) return 'warning';
      return 'critical';
    } else {
      if (value <= threshold.excellent) return 'excellent';
      if (value <= threshold.good) return 'good';
      if (value <= threshold.warning) return 'warning';
      return 'critical';
    }
  };

  const getStatusColor = (status: string) => {
    const colors = {
      excellent: '#52c41a',
      good: '#1890ff',
      warning: '#faad14',
      critical: '#ff4d4f'
    };
    return colors[status as keyof typeof colors] || '#d9d9d9';
  };

  const getStatusText = (status: string) => {
    const texts = {
      excellent: '优秀',
      good: '良好',
      warning: '告警',
      critical: '严重'
    };
    return texts[status as keyof typeof texts] || '未知';
  };

  const columns = [
    {
      title: '设备信息',
      key: 'deviceInfo',
      width: 200,
      render: (_, record: any) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.deviceName}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.region} | {record.deviceType}
          </div>
        </div>
      ),
    },
    {
      title: '延迟 (ms)',
      dataIndex: 'latency',
      key: 'latency',
      width: 100,
      render: (latency: number) => (
        <div>
          <div style={{ color: getStatusColor(getPerformanceStatus(latency, 'latency')) }}>
            {latency}
          </div>
          <Progress
            percent={(100 - latency) > 0 ? (100 - latency) : 0}
            size="small"
            strokeColor={getStatusColor(getPerformanceStatus(latency, 'latency'))}
            showInfo={false}
          />
        </div>
      ),
    },
    {
      title: '吞吐量 (Gbps)',
      dataIndex: 'throughput',
      key: 'throughput',
      width: 120,
      render: (throughput: number) => (
        <div>
          <div style={{ color: getStatusColor(getPerformanceStatus(throughput, 'throughput')) }}>
            {throughput}
          </div>
          <Progress
            percent={throughput * 10}
            size="small"
            strokeColor={getStatusColor(getPerformanceStatus(throughput, 'throughput'))}
            showInfo={false}
          />
        </div>
      ),
    },
    {
      title: '丢包率 (%)',
      dataIndex: 'packetLoss',
      key: 'packetLoss',
      width: 100,
      render: (packetLoss: number) => (
        <div>
          <div style={{ color: getStatusColor(getPerformanceStatus(packetLoss, 'packetLoss')) }}>
            {(packetLoss * 100).toFixed(2)}%
          </div>
          <Progress
            percent={100 - (packetLoss * 1000)}
            size="small"
            strokeColor={getStatusColor(getPerformanceStatus(packetLoss, 'packetLoss'))}
            showInfo={false}
          />
        </div>
      ),
    },
    {
      title: '抖动 (ms)',
      dataIndex: 'jitter',
      key: 'jitter',
      width: 100,
      render: (jitter: number) => (
        <div>
          <div style={{ color: getStatusColor(getPerformanceStatus(jitter, 'jitter')) }}>
            {jitter}
          </div>
          <Progress
            percent={100 - jitter * 10}
            size="small"
            strokeColor={getStatusColor(getPerformanceStatus(jitter, 'jitter'))}
            showInfo={false}
          />
        </div>
      ),
    },
    {
      title: '带宽利用率',
      dataIndex: 'bandwidthUtil',
      key: 'bandwidthUtil',
      width: 120,
      render: (util: number) => (
        <Progress
          percent={util}
          size="small"
          strokeColor={getStatusColor(getPerformanceStatus(util, 'utilization'))}
          format={(percent) => `${percent}%`}
        />
      ),
    },
    {
      title: 'CPU利用率',
      dataIndex: 'cpuUtil',
      key: 'cpuUtil',
      width: 120,
      render: (util: number) => (
        <Progress
          percent={util}
          size="small"
          strokeColor={getStatusColor(getPerformanceStatus(util, 'utilization'))}
          format={(percent) => `${percent}%`}
        />
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '最后更新',
      dataIndex: 'lastUpdate',
      key: 'lastUpdate',
      width: 150,
    }
  ];

  const filteredData = performanceData.filter(item => {
    const regionMatch = selectedRegion === 'all' || item.region === selectedRegion;
    return regionMatch;
  });

  return (
    <div>
      {/* 控制面板 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Alert
            message="性能指标监控"
            description="实时监控网络设备性能指标，包括延迟、吞吐量、丢包率等关键指标"
            type="info"
            showIcon
            action={
              <Space>
                <Tooltip title="实时模式">
                  <Switch
                    checked={realTimeMode}
                    onChange={setRealTimeMode}
                    checkedChildren="实时"
                    unCheckedChildren="历史"
                  />
                </Tooltip>
                <Button icon={<ReloadOutlined />} size="small">
                  刷新数据
                </Button>
              </Space>
            }
          />
        </Col>
      </Row>

      {/* 性能概览 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={3}>
          <Card>
            <Statistic
              title="平均延迟"
              value={performanceOverview.avgLatency}
              suffix="ms"
              precision={1}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: getStatusColor(getPerformanceStatus(performanceOverview.avgLatency, 'latency')) }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={3}>
          <Card>
            <Statistic
              title="平均吞吐量"
              value={performanceOverview.avgThroughput}
              suffix="Gbps"
              precision={1}
              prefix={<ThunderboltOutlined />}
              valueStyle={{ color: getStatusColor(getPerformanceStatus(performanceOverview.avgThroughput, 'throughput')) }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={3}>
          <Card>
            <Statistic
              title="平均丢包率"
              value={performanceOverview.avgPacketLoss}
              suffix="%"
              precision={3}
              prefix={<WarningOutlined />}
              valueStyle={{ color: getStatusColor(getPerformanceStatus(performanceOverview.avgPacketLoss, 'packetLoss')) }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={3}>
          <Card>
            <Statistic
              title="平均抖动"
              value={performanceOverview.avgJitter}
              suffix="ms"
              precision={1}
              prefix={<WifiOutlined />}
              valueStyle={{ color: getStatusColor(getPerformanceStatus(performanceOverview.avgJitter, 'jitter')) }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={3}>
          <Card>
            <Statistic
              title="带宽利用率"
              value={performanceOverview.avgBandwidthUtil}
              suffix="%"
              precision={1}
              prefix={<LineChartOutlined />}
              valueStyle={{ color: getStatusColor(getPerformanceStatus(performanceOverview.avgBandwidthUtil, 'utilization')) }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={3}>
          <Card>
            <Statistic
              title="CPU利用率"
              value={performanceOverview.avgCpuUtil}
              suffix="%"
              precision={1}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: getStatusColor(getPerformanceStatus(performanceOverview.avgCpuUtil, 'utilization')) }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={3}>
          <Card>
            <Statistic
              title="内存利用率"
              value={performanceOverview.avgMemoryUtil}
              suffix="%"
              precision={1}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: getStatusColor(getPerformanceStatus(performanceOverview.avgMemoryUtil, 'utilization')) }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={3}>
          <Card>
            <Statistic
              title="磁盘利用率"
              value={performanceOverview.avgDiskUtil}
              suffix="%"
              precision={1}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: getStatusColor(getPerformanceStatus(performanceOverview.avgDiskUtil, 'utilization')) }}
            />
          </Card>
        </Col>
      </Row>

      {/* 详细数据 */}
      <Card>
        <Tabs defaultActiveKey="realtime">
          <TabPane tab="实时性能" key="realtime">
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Select
                  value={selectedRegion}
                  onChange={setSelectedRegion}
                  style={{ width: 120 }}
                >
                  <Option value="all">全部区域</Option>
                  <Option value="北京">北京</Option>
                  <Option value="上海">上海</Option>
                  <Option value="广州">广州</Option>
                  <Option value="深圳">深圳</Option>
                  <Option value="杭州">杭州</Option>
                </Select>
                <Select
                  value={selectedMetric}
                  onChange={setSelectedMetric}
                  style={{ width: 140 }}
                >
                  <Option value="all">全部指标</Option>
                  <Option value="latency">延迟</Option>
                  <Option value="throughput">吞吐量</Option>
                  <Option value="packetLoss">丢包率</Option>
                  <Option value="jitter">抖动</Option>
                </Select>
                <RangePicker size="small" />
                <Button icon={<DownloadOutlined />} size="small">
                  导出数据
                </Button>
              </Space>
            </div>
            <Table
              columns={columns}
              dataSource={filteredData}
              pagination={{
                total: filteredData.length,
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
              }}
              scroll={{ x: 1200 }}
            />
          </TabPane>

          <TabPane tab="性能趋势" key="trend">
            <Row gutter={16}>
              <Col span={12}>
                <Card title="延迟趋势" size="small">
                  <div style={{ height: '200px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#999' }}>
                    延迟趋势图表 (可集成 ECharts)
                  </div>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="吞吐量趋势" size="small">
                  <div style={{ height: '200px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#999' }}>
                    吞吐量趋势图表 (可集成 ECharts)
                  </div>
                </Card>
              </Col>
            </Row>
            <Row gutter={16} style={{ marginTop: 16 }}>
              <Col span={12}>
                <Card title="丢包率趋势" size="small">
                  <div style={{ height: '200px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#999' }}>
                    丢包率趋势图表 (可集成 ECharts)
                  </div>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="利用率趋势" size="small">
                  <div style={{ height: '200px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#999' }}>
                    利用率趋势图表 (可集成 ECharts)
                  </div>
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="阈值配置" key="thresholds">
            <Row gutter={16}>
              <Col span={12}>
                <Card title="性能阈值设置" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div>
                      <strong>延迟阈值 (ms)</strong>
                      <div>优秀: &le; {thresholds.latency.excellent} | 良好: &le; {thresholds.latency.good} | 告警: &le; {thresholds.latency.warning}</div>
                    </div>
                    <div>
                      <strong>吞吐量阈值 (Gbps)</strong>
                      <div>优秀: &ge; {thresholds.throughput.excellent} | 良好: &ge; {thresholds.throughput.good} | 告警: &ge; {thresholds.throughput.warning}</div>
                    </div>
                    <div>
                      <strong>丢包率阈值 (%)</strong>
                      <div>优秀: &le; {thresholds.packetLoss.excellent} | 良好: &le; {thresholds.packetLoss.good} | 告警: &le; {thresholds.packetLoss.warning}</div>
                    </div>
                    <div>
                      <strong>抖动阈值 (ms)</strong>
                      <div>优秀: &le; {thresholds.jitter.excellent} | 良好: &le; {thresholds.jitter.good} | 告警: &le; {thresholds.jitter.warning}</div>
                    </div>
                  </Space>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="利用率阈值设置" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div>
                      <strong>CPU/内存/磁盘利用率阈值 (%)</strong>
                      <div>优秀: &le; {thresholds.utilization.excellent}%</div>
                      <div>良好: &le; {thresholds.utilization.good}%</div>
                      <div>告警: &le; {thresholds.utilization.warning}%</div>
                      <div>严重: &gt; {thresholds.utilization.warning}%</div>
                    </div>
                  </Space>
                </Card>
              </Col>
            </Row>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default PerformanceMetrics;
