import React from 'react';
import { <PERSON>, Row, Col, Statistic, Alert } from 'antd';
import { Bar<PERSON><PERSON>Outlined, PieChartOutlined, LineChartOutlined } from '@ant-design/icons';

const StatisticsTest: React.FC = () => {
  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Alert
            message="统计分析中心"
            description="提供多维度数据统计分析，支持自定义指标和可视化展示"
            type="info"
            showIcon
          />
        </Col>
      </Row>

      <Row gutter={16}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="数据点总数"
              value={15680}
              prefix={<BarChartOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="分析模型"
              value={12}
              prefix={<PieChartOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="数据质量"
              value={96.2}
              suffix="%"
              precision={1}
              prefix={<LineChartOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      <Card style={{ marginTop: 16 }}>
        <h3>统计分析功能</h3>
        <p>这里是统计分析页面的内容。</p>
        <ul>
          <li>多维度数据分析</li>
          <li>相关性分析</li>
          <li>分布分析</li>
          <li>回归分析</li>
        </ul>
      </Card>
    </div>
  );
};

export default StatisticsTest;
