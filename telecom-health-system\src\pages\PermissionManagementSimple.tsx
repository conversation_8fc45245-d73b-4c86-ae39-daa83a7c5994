import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Table,
  Tree,
  Tag,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Alert,
  Statistic,
  Tabs,
  message
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  TeamOutlined,
  CrownOutlined,
  SafetyOutlined,
  KeyOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { TabPane } = Tabs;

const PermissionManagementSimple: React.FC = () => {
  const [activeTab, setActiveTab] = useState('roles');
  const [isRoleModalVisible, setIsRoleModalVisible] = useState(false);
  const [isPermissionModalVisible, setIsPermissionModalVisible] = useState(false);
  const [selectedRole, setSelectedRole] = useState(null);
  const [form] = Form.useForm();

  const permissionStats = {
    totalRoles: 8,
    totalPermissions: 45,
    activeUsers: 38,
    systemModules: 12
  };

  const rolesData = [
    {
      key: '1',
      id: 'R001',
      name: '超级管理员',
      code: 'super_admin',
      description: '拥有系统所有权限',
      userCount: 2,
      status: 'active',
      createTime: '2023-01-01 00:00'
    },
    {
      key: '2',
      id: 'R002',
      name: '系统管理员',
      code: 'admin',
      description: '管理用户和基本系统配置',
      userCount: 3,
      status: 'active',
      createTime: '2023-01-01 00:00'
    },
    {
      key: '3',
      id: 'R003',
      name: '运维工程师',
      code: 'operator',
      description: '网络监控和设备维护权限',
      userCount: 15,
      status: 'active',
      createTime: '2023-01-01 00:00'
    },
    {
      key: '4',
      id: 'R004',
      name: '业务分析师',
      code: 'analyst',
      description: '数据分析和报表查看权限',
      userCount: 12,
      status: 'active',
      createTime: '2023-01-01 00:00'
    },
    {
      key: '5',
      id: 'R005',
      name: '客服专员',
      code: 'customer_service',
      description: '客户管理和工单处理权限',
      userCount: 6,
      status: 'active',
      createTime: '2023-01-01 00:00'
    }
  ];

  const permissionTree = [
    {
      title: '用户管理',
      key: 'user_module',
      children: [
        { title: '查看用户列表', key: 'user_view' },
        { title: '新增用户', key: 'user_create' },
        { title: '编辑用户', key: 'user_edit' },
        { title: '删除用户', key: 'user_delete' }
      ]
    },
    {
      title: '客户管理',
      key: 'customer_module',
      children: [
        { title: '查看客户信息', key: 'customer_view' },
        { title: '编辑客户信息', key: 'customer_edit' },
        { title: '客户分类管理', key: 'customer_category' }
      ]
    },
    {
      title: '网络监控',
      key: 'network_module',
      children: [
        { title: '网络状态查看', key: 'network_status' },
        { title: '设备监控', key: 'device_monitor' },
        { title: '告警管理', key: 'alert_manage' }
      ]
    },
    {
      title: '系统管理',
      key: 'system_module',
      children: [
        { title: '权限管理', key: 'permission_manage' },
        { title: '系统配置', key: 'system_config' },
        { title: '操作日志', key: 'operation_log' }
      ]
    }
  ];

  const roleColumns = [
    {
      title: '角色信息',
      key: 'roleInfo',
      render: (_, record: any) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.name}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.code} | {record.id}
          </div>
        </div>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '用户数量',
      dataIndex: 'userCount',
      key: 'userCount',
      width: 100,
      render: (count: number) => (
        <Tag color="blue">{count} 人</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 150,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record: any) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditRole(record)}
          >
            编辑
          </Button>
          <Button
            type="text"
            icon={<KeyOutlined />}
            onClick={() => handleAssignPermissions(record)}
          >
            权限
          </Button>
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteRole(record)}
          >
            删除
          </Button>
        </Space>
      ),
    }
  ];

  const handleEditRole = (role: any) => {
    setSelectedRole(role);
    form.setFieldsValue(role);
    setIsRoleModalVisible(true);
  };

  const handleAssignPermissions = (role: any) => {
    setSelectedRole(role);
    setIsPermissionModalVisible(true);
  };

  const handleDeleteRole = (role: any) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除角色 "${role.name}" 吗？`,
      onOk() {
        message.success('删除成功');
      },
    });
  };

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Alert
            message="权限管理"
            description="管理系统角色和权限，控制用户对系统功能的访问权限"
            type="info"
            showIcon
          />
        </Col>
      </Row>

      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="角色总数"
              value={permissionStats.totalRoles}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="权限总数"
              value={permissionStats.totalPermissions}
              prefix={<SafetyOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="活跃用户"
              value={permissionStats.activeUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="系统模块"
              value={permissionStats.systemModules}
              prefix={<CrownOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="角色管理" key="roles">
            <div style={{ marginBottom: 16 }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setSelectedRole(null);
                  form.resetFields();
                  setIsRoleModalVisible(true);
                }}
              >
                新建角色
              </Button>
            </div>
            <Table
              columns={roleColumns}
              dataSource={rolesData}
              pagination={{
                total: rolesData.length,
                pageSize: 10,
                showSizeChanger: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
              }}
            />
          </TabPane>

          <TabPane tab="权限树" key="permissions">
            <Card title="系统权限结构" size="small">
              <Tree
                checkable
                defaultExpandAll
                treeData={permissionTree}
                onCheck={(checkedKeys) => {
                  console.log('选中的权限:', checkedKeys);
                }}
              />
            </Card>
          </TabPane>
        </Tabs>
      </Card>

      <Modal
        title={selectedRole ? '编辑角色' : '新建角色'}
        open={isRoleModalVisible}
        onOk={() => {
          form.validateFields().then(() => {
            message.success(selectedRole ? '更新成功' : '创建成功');
            setIsRoleModalVisible(false);
          });
        }}
        onCancel={() => setIsRoleModalVisible(false)}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item name="name" label="角色名称" rules={[{ required: true }]}>
            <Input placeholder="请输入角色名称" />
          </Form.Item>
          <Form.Item name="code" label="角色代码" rules={[{ required: true }]}>
            <Input placeholder="请输入角色代码" />
          </Form.Item>
          <Form.Item name="description" label="角色描述">
            <Input.TextArea rows={3} placeholder="请输入角色描述" />
          </Form.Item>
          <Form.Item name="status" label="状态" rules={[{ required: true }]}>
            <Select placeholder="请选择状态">
              <Option value="active">启用</Option>
              <Option value="inactive">禁用</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="分配权限"
        open={isPermissionModalVisible}
        onOk={() => {
          message.success('权限分配成功');
          setIsPermissionModalVisible(false);
        }}
        onCancel={() => setIsPermissionModalVisible(false)}
        width={800}
      >
        {selectedRole && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <strong>角色：</strong>{(selectedRole as any).name}
            </div>
            <Tree
              checkable
              defaultExpandAll
              treeData={permissionTree}
              onCheck={(checkedKeys) => {
                console.log('选中的权限:', checkedKeys);
              }}
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default PermissionManagementSimple;
