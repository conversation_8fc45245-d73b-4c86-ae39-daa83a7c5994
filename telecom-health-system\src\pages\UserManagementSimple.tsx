import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Table,
  Tag,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Alert,
  Statistic,
  Avatar,
  Switch,
  message
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  TeamOutlined,
  CrownOutlined,
  LockOutlined,
  UnlockOutlined
} from '@ant-design/icons';

const { Option } = Select;

const UserManagementSimple: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [form] = Form.useForm();

  const userStats = {
    total: 45,
    active: 38,
    admin: 5,
    operator: 15,
    viewer: 25
  };

  const userData = [
    {
      key: '1',
      id: 'U001',
      username: 'admin',
      name: '系统管理员',
      email: '<EMAIL>',
      role: 'admin',
      department: 'IT部门',
      status: 'active',
      lastLogin: '2024-01-15 14:30',
      createTime: '2023-01-01 00:00'
    },
    {
      key: '2',
      id: 'U002',
      username: 'operator01',
      name: '张运维',
      email: '<EMAIL>',
      role: 'operator',
      department: '运维部门',
      status: 'active',
      lastLogin: '2024-01-15 13:45',
      createTime: '2023-03-15 10:30'
    },
    {
      key: '3',
      id: 'U003',
      username: 'viewer01',
      name: '李观察员',
      email: '<EMAIL>',
      role: 'viewer',
      department: '业务部门',
      status: 'inactive',
      lastLogin: '2024-01-10 16:20',
      createTime: '2023-06-20 14:15'
    },
    {
      key: '4',
      id: 'U004',
      username: 'operator02',
      name: '王技术员',
      email: '<EMAIL>',
      role: 'operator',
      department: '运维部门',
      status: 'active',
      lastLogin: '2024-01-15 12:30',
      createTime: '2023-08-10 09:20'
    }
  ];

  const getRoleColor = (role: string) => {
    const colors = {
      admin: 'red',
      operator: 'blue',
      viewer: 'green'
    };
    return colors[role as keyof typeof colors] || 'default';
  };

  const getRoleText = (role: string) => {
    const texts = {
      admin: '管理员',
      operator: '运维员',
      viewer: '观察员'
    };
    return texts[role as keyof typeof texts] || '未知';
  };

  const getRoleIcon = (role: string) => {
    const icons = {
      admin: <CrownOutlined />,
      operator: <UserOutlined />,
      viewer: <TeamOutlined />
    };
    return icons[role as keyof typeof icons] || <UserOutlined />;
  };

  const columns = [
    {
      title: '用户信息',
      key: 'userInfo',
      render: (_, record: any) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar icon={<UserOutlined />} style={{ marginRight: 12 }} />
          <div>
            <div style={{ fontWeight: 'bold' }}>{record.name}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {record.username} | {record.id}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      width: 120,
      render: (role: string) => (
        <Tag color={getRoleColor(role)} icon={getRoleIcon(role)}>
          {getRoleText(role)}
        </Tag>
      ),
    },
    {
      title: '部门',
      dataIndex: 'department',
      key: 'department',
      width: 120,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: 200,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string, record: any) => (
        <Switch
          checked={status === 'active'}
          checkedChildren="启用"
          unCheckedChildren="禁用"
          onChange={(checked) => {
            message.success(checked ? '用户已启用' : '用户已禁用');
          }}
        />
      ),
    },
    {
      title: '最后登录',
      dataIndex: 'lastLogin',
      key: 'lastLogin',
      width: 150,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record: any) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          />
          <Button
            type="text"
            icon={record.status === 'active' ? <LockOutlined /> : <UnlockOutlined />}
            onClick={() => handleToggleStatus(record)}
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          />
        </Space>
      ),
    }
  ];

  const handleEdit = (user: any) => {
    setSelectedUser(user);
    form.setFieldsValue(user);
    setIsModalVisible(true);
  };

  const handleToggleStatus = (user: any) => {
    const action = user.status === 'active' ? '禁用' : '启用';
    Modal.confirm({
      title: `确认${action}用户`,
      content: `确定要${action}用户 "${user.name}" 吗？`,
      onOk() {
        message.success(`用户已${action}`);
      },
    });
  };

  const handleDelete = (user: any) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除用户 "${user.name}" 吗？此操作不可恢复。`,
      onOk() {
        message.success('删除成功');
      },
    });
  };

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Alert
            message="用户管理"
            description="管理系统用户账号、角色权限和访问控制"
            type="info"
            showIcon
          />
        </Col>
      </Row>

      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="用户总数"
              value={userStats.total}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="活跃用户"
              value={userStats.active}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="管理员"
              value={userStats.admin}
              prefix={<CrownOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="运维员"
              value={userStats.operator}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      <Card
        title="用户列表"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setSelectedUser(null);
              form.resetFields();
              setIsModalVisible(true);
            }}
          >
            新增用户
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={userData}
          pagination={{
            total: userData.length,
            pageSize: 10,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1000 }}
        />
      </Card>

      <Modal
        title={selectedUser ? '编辑用户' : '新增用户'}
        open={isModalVisible}
        onOk={() => {
          form.validateFields().then(() => {
            message.success(selectedUser ? '更新成功' : '创建成功');
            setIsModalVisible(false);
          });
        }}
        onCancel={() => setIsModalVisible(false)}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="username" label="用户名" rules={[{ required: true }]}>
                <Input placeholder="请输入用户名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="name" label="姓名" rules={[{ required: true }]}>
                <Input placeholder="请输入姓名" />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item name="email" label="邮箱" rules={[{ required: true, type: 'email' }]}>
            <Input placeholder="请输入邮箱" />
          </Form.Item>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="role" label="角色" rules={[{ required: true }]}>
                <Select placeholder="请选择角色">
                  <Option value="admin">管理员</Option>
                  <Option value="operator">运维员</Option>
                  <Option value="viewer">观察员</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="department" label="部门" rules={[{ required: true }]}>
                <Select placeholder="请选择部门">
                  <Option value="IT部门">IT部门</Option>
                  <Option value="运维部门">运维部门</Option>
                  <Option value="业务部门">业务部门</Option>
                  <Option value="财务部门">财务部门</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          {!selectedUser && (
            <Form.Item name="password" label="密码" rules={[{ required: true, min: 6 }]}>
              <Input.Password placeholder="请输入密码（至少6位）" />
            </Form.Item>
          )}
        </Form>
      </Modal>
    </div>
  );
};

export default UserManagementSimple;
