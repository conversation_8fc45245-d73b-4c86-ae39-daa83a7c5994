import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Table,
  Tag,
  Progress,
  Statistic,
  Select,
  DatePicker,
  Button,
  Space,
  Alert,
  Tooltip,
  Badge,
  Descriptions,
  Modal,
  Tabs
} from 'antd';
import {
  ReloadOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  SettingOutlined,
  EyeOutlined,
  <PERSON>boltOutlined,
  DatabaseOutlined,
  WifiOutlined,
  HddOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

const DeviceMonitor: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [selectedRegion, setSelectedRegion] = useState('all');
  const [selectedType, setSelectedType] = useState('all');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState(null);

  // 模拟实时数据更新
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        // 模拟数据更新
        console.log('Refreshing device data...');
      }, 30000);
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  // 设备统计数据
  const deviceStats = {
    total: 2856,
    online: 2734,
    warning: 89,
    offline: 33,
    avgCpuUsage: 65.8,
    avgMemoryUsage: 72.3,
    avgDiskUsage: 45.2,
    networkTraffic: 8.5
  };

  // 设备列表数据
  const deviceData = [
    {
      key: '1',
      deviceId: 'RT-BJ-001',
      deviceName: '北京核心路由器-001',
      type: '核心路由器',
      region: '北京',
      location: '北京数据中心A栋',
      status: 'online',
      health: 98,
      cpuUsage: 45,
      memoryUsage: 67,
      diskUsage: 32,
      temperature: 42,
      uptime: '99.99%',
      lastHeartbeat: '2024-01-15 14:30:25',
      manufacturer: 'Huawei',
      model: 'NE8000-X8',
      ip: '*************'
    },
    {
      key: '2',
      deviceId: 'SW-SH-015',
      deviceName: '上海接入交换机-015',
      type: '接入交换机',
      region: '上海',
      location: '上海数据中心B栋',
      status: 'warning',
      health: 75,
      cpuUsage: 85,
      memoryUsage: 92,
      diskUsage: 78,
      temperature: 68,
      uptime: '99.8%',
      lastHeartbeat: '2024-01-15 14:30:20',
      manufacturer: 'Cisco',
      model: 'Catalyst 9500',
      ip: '*************'
    },
    {
      key: '3',
      deviceId: 'FW-GZ-008',
      deviceName: '广州防火墙-008',
      type: '防火墙',
      region: '广州',
      location: '广州数据中心C栋',
      status: 'online',
      health: 92,
      cpuUsage: 38,
      memoryUsage: 55,
      diskUsage: 41,
      temperature: 39,
      uptime: '99.95%',
      lastHeartbeat: '2024-01-15 14:30:18',
      manufacturer: 'Fortinet',
      model: 'FortiGate 3000D',
      ip: '************'
    },
    {
      key: '4',
      deviceId: 'LB-SZ-003',
      deviceName: '深圳负载均衡器-003',
      type: '负载均衡器',
      region: '深圳',
      location: '深圳数据中心D栋',
      status: 'offline',
      health: 0,
      cpuUsage: 0,
      memoryUsage: 0,
      diskUsage: 0,
      temperature: 0,
      uptime: '0%',
      lastHeartbeat: '2024-01-15 13:45:12',
      manufacturer: 'F5',
      model: 'BIG-IP 8900',
      ip: '************'
    },
    {
      key: '5',
      deviceId: 'AP-HZ-056',
      deviceName: '杭州无线AP-056',
      type: '无线AP',
      region: '杭州',
      location: '杭州办公楼5层',
      status: 'online',
      health: 88,
      cpuUsage: 28,
      memoryUsage: 45,
      diskUsage: 25,
      temperature: 35,
      uptime: '99.9%',
      lastHeartbeat: '2024-01-15 14:30:15',
      manufacturer: 'Aruba',
      model: 'AP-515',
      ip: '************'
    }
  ];

  const getStatusColor = (status: string) => {
    const colors = {
      online: 'green',
      warning: 'orange',
      offline: 'red',
      maintenance: 'blue'
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getStatusText = (status: string) => {
    const texts = {
      online: '在线',
      warning: '告警',
      offline: '离线',
      maintenance: '维护'
    };
    return texts[status as keyof typeof texts] || '未知';
  };

  const getStatusIcon = (status: string) => {
    const icons = {
      online: <CheckCircleOutlined />,
      warning: <ExclamationCircleOutlined />,
      offline: <CloseCircleOutlined />,
      maintenance: <SettingOutlined />
    };
    return icons[status as keyof typeof icons] || <CheckCircleOutlined />;
  };

  const getUsageColor = (usage: number) => {
    if (usage >= 90) return '#ff4d4f';
    if (usage >= 80) return '#faad14';
    if (usage >= 70) return '#1890ff';
    return '#52c41a';
  };

  const columns = [
    {
      title: '设备信息',
      key: 'deviceInfo',
      width: 200,
      render: (_, record: any) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.deviceName}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.deviceId} | {record.type}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.manufacturer} {record.model}
          </div>
        </div>
      ),
    },
    {
      title: '位置',
      key: 'location',
      width: 150,
      render: (_, record: any) => (
        <div>
          <div>{record.region}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.location}</div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Badge
          status={status === 'online' ? 'success' : status === 'warning' ? 'warning' : 'error'}
          text={
            <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
              {getStatusText(status)}
            </Tag>
          }
        />
      ),
    },
    {
      title: '健康度',
      dataIndex: 'health',
      key: 'health',
      width: 120,
      render: (health: number) => (
        <Progress
          percent={health}
          size="small"
          strokeColor={health >= 90 ? '#52c41a' : health >= 70 ? '#1890ff' : '#faad14'}
          format={(percent) => `${percent}%`}
        />
      ),
    },
    {
      title: 'CPU使用率',
      dataIndex: 'cpuUsage',
      key: 'cpuUsage',
      width: 120,
      render: (usage: number) => (
        <Progress
          percent={usage}
          size="small"
          strokeColor={getUsageColor(usage)}
          format={(percent) => `${percent}%`}
        />
      ),
    },
    {
      title: '内存使用率',
      dataIndex: 'memoryUsage',
      key: 'memoryUsage',
      width: 120,
      render: (usage: number) => (
        <Progress
          percent={usage}
          size="small"
          strokeColor={getUsageColor(usage)}
          format={(percent) => `${percent}%`}
        />
      ),
    },
    {
      title: '温度',
      dataIndex: 'temperature',
      key: 'temperature',
      width: 80,
      render: (temp: number) => (
        <span style={{ color: temp > 60 ? '#ff4d4f' : temp > 50 ? '#faad14' : '#52c41a' }}>
          {temp}°C
        </span>
      ),
    },
    {
      title: '可用性',
      dataIndex: 'uptime',
      key: 'uptime',
      width: 100,
    },
    {
      title: '最后心跳',
      dataIndex: 'lastHeartbeat',
      key: 'lastHeartbeat',
      width: 150,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record: any) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => showDeviceDetail(record)}
            />
          </Tooltip>
          <Tooltip title="重启设备">
            <Button
              type="text"
              icon={<ReloadOutlined />}
              onClick={() => handleRestart(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const showDeviceDetail = (device: any) => {
    setSelectedDevice(device);
    setDetailModalVisible(true);
  };

  const handleRestart = (device: any) => {
    Modal.confirm({
      title: '确认重启设备',
      content: `确定要重启设备 "${device.deviceName}" 吗？`,
      onOk() {
        console.log('Restarting device:', device.deviceId);
      },
    });
  };

  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  const filteredData = deviceData.filter(device => {
    const regionMatch = selectedRegion === 'all' || device.region === selectedRegion;
    const typeMatch = selectedType === 'all' || device.type === selectedType;
    return regionMatch && typeMatch;
  });

  return (
    <div>
      {/* 告警信息 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Alert
            message="设备监控中心"
            description="实时监控网络设备状态，自动刷新间隔30秒"
            type="info"
            showIcon
            action={
              <Space>
                <Button
                  size="small"
                  type={autoRefresh ? 'primary' : 'default'}
                  onClick={() => setAutoRefresh(!autoRefresh)}
                >
                  {autoRefresh ? '停止自动刷新' : '开启自动刷新'}
                </Button>
                <Button
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={loading}
                >
                  手动刷新
                </Button>
              </Space>
            }
          />
        </Col>
      </Row>

      {/* 设备统计 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={3}>
          <Card>
            <Statistic
              title="设备总数"
              value={deviceStats.total}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={3}>
          <Card>
            <Statistic
              title="在线设备"
              value={deviceStats.online}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={3}>
          <Card>
            <Statistic
              title="告警设备"
              value={deviceStats.warning}
              prefix={<WarningOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={3}>
          <Card>
            <Statistic
              title="离线设备"
              value={deviceStats.offline}
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={3}>
          <Card>
            <Statistic
              title="平均CPU"
              value={deviceStats.avgCpuUsage}
              suffix="%"
              precision={1}
              prefix={<ThunderboltOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={3}>
          <Card>
            <Statistic
              title="平均内存"
              value={deviceStats.avgMemoryUsage}
              suffix="%"
              precision={1}
              prefix={<HddOutlined />}
              valueStyle={{ color: '#eb2f96' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={3}>
          <Card>
            <Statistic
              title="平均磁盘"
              value={deviceStats.avgDiskUsage}
              suffix="%"
              precision={1}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#13c2c2' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={3}>
          <Card>
            <Statistic
              title="网络流量"
              value={deviceStats.networkTraffic}
              suffix="Gbps"
              precision={1}
              prefix={<WifiOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 设备列表 */}
      <Card
        title="设备列表"
        extra={
          <Space>
            <Select
              value={selectedRegion}
              onChange={setSelectedRegion}
              style={{ width: 120 }}
            >
              <Option value="all">全部区域</Option>
              <Option value="北京">北京</Option>
              <Option value="上海">上海</Option>
              <Option value="广州">广州</Option>
              <Option value="深圳">深圳</Option>
              <Option value="杭州">杭州</Option>
            </Select>
            <Select
              value={selectedType}
              onChange={setSelectedType}
              style={{ width: 140 }}
            >
              <Option value="all">全部类型</Option>
              <Option value="核心路由器">核心路由器</Option>
              <Option value="接入交换机">接入交换机</Option>
              <Option value="防火墙">防火墙</Option>
              <Option value="负载均衡器">负载均衡器</Option>
              <Option value="无线AP">无线AP</Option>
            </Select>
            <RangePicker size="small" />
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={filteredData}
          loading={loading}
          pagination={{
            total: filteredData.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1400 }}
        />
      </Card>

      {/* 设备详情模态框 */}
      <Modal
        title="设备详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedDevice && (
          <Tabs defaultActiveKey="basic">
            <TabPane tab="基本信息" key="basic">
              <Descriptions column={2} bordered>
                <Descriptions.Item label="设备ID">{(selectedDevice as any).deviceId}</Descriptions.Item>
                <Descriptions.Item label="设备名称">{(selectedDevice as any).deviceName}</Descriptions.Item>
                <Descriptions.Item label="设备类型">{(selectedDevice as any).type}</Descriptions.Item>
                <Descriptions.Item label="制造商">{(selectedDevice as any).manufacturer}</Descriptions.Item>
                <Descriptions.Item label="型号">{(selectedDevice as any).model}</Descriptions.Item>
                <Descriptions.Item label="IP地址">{(selectedDevice as any).ip}</Descriptions.Item>
                <Descriptions.Item label="所在区域">{(selectedDevice as any).region}</Descriptions.Item>
                <Descriptions.Item label="物理位置">{(selectedDevice as any).location}</Descriptions.Item>
                <Descriptions.Item label="运行状态">
                  <Tag color={getStatusColor((selectedDevice as any).status)}>
                    {getStatusText((selectedDevice as any).status)}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="健康度">
                  <Progress percent={(selectedDevice as any).health} size="small" />
                </Descriptions.Item>
              </Descriptions>
            </TabPane>
            <TabPane tab="性能指标" key="performance">
              <Row gutter={16}>
                <Col span={12}>
                  <Card title="CPU使用率">
                    <Progress
                      type="circle"
                      percent={(selectedDevice as any).cpuUsage}
                      strokeColor={getUsageColor((selectedDevice as any).cpuUsage)}
                    />
                  </Card>
                </Col>
                <Col span={12}>
                  <Card title="内存使用率">
                    <Progress
                      type="circle"
                      percent={(selectedDevice as any).memoryUsage}
                      strokeColor={getUsageColor((selectedDevice as any).memoryUsage)}
                    />
                  </Card>
                </Col>
              </Row>
            </TabPane>
          </Tabs>
        )}
      </Modal>
    </div>
  );
};

export default DeviceMonitor;
