import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Table,
  Select,
  DatePicker,
  Input,
  Button,
  Tag,
  Space,
  Alert,
  Statistic,
  Modal,
  Descriptions,
  Timeline,
  message
} from 'antd';
import {
  SearchOutlined,
  EyeOutlined,
  DownloadOutlined,
  ReloadOutlined,
  FileTextOutlined,
  UserOutlined,
  ClockCircleOutlined,
  SafetyOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Search } = Input;

const OperationLog: React.FC = () => {
  const [selectedLog, setSelectedLog] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [filters, setFilters] = useState({
    operation: 'all',
    user: 'all',
    result: 'all',
    dateRange: null
  });

  const logStats = {
    totalLogs: 15680,
    todayLogs: 234,
    successRate: 98.5,
    errorLogs: 23
  };

  const operationLogs = [
    {
      key: '1',
      id: 'LOG-2024-001',
      timestamp: '2024-01-15 14:30:25',
      user: '张管理员',
      userRole: '系统管理员',
      operation: '用户管理',
      action: '新增用户',
      target: '李技术员',
      result: 'success',
      ip: '*************',
      userAgent: 'Chrome/120.0.0.0',
      details: '新增用户账号，角色：运维工程师',
      duration: 1.2
    },
    {
      key: '2',
      id: 'LOG-2024-002',
      timestamp: '2024-01-15 14:25:18',
      user: '王运维',
      userRole: '运维工程师',
      operation: '设备管理',
      action: '重启设备',
      target: 'RT-BJ-001',
      result: 'success',
      ip: '*************',
      userAgent: 'Chrome/120.0.0.0',
      details: '重启北京核心路由器，原因：性能异常',
      duration: 45.6
    },
    {
      key: '3',
      id: 'LOG-2024-003',
      timestamp: '2024-01-15 14:20:42',
      user: '刘分析师',
      userRole: '业务分析师',
      operation: '数据导出',
      action: '导出报表',
      target: '客户信息表',
      result: 'success',
      ip: '*************',
      userAgent: 'Firefox/*********',
      details: '导出客户信息表，格式：Excel，记录数：3414',
      duration: 8.3
    },
    {
      key: '4',
      id: 'LOG-2024-004',
      timestamp: '2024-01-15 14:15:33',
      user: '陈客服',
      userRole: '客服专员',
      operation: '工单管理',
      action: '创建工单',
      target: 'TK-2024-005',
      result: 'success',
      ip: '*************',
      userAgent: 'Chrome/120.0.0.0',
      details: '创建故障工单，客户：ABC公司，问题：网络中断',
      duration: 2.1
    },
    {
      key: '5',
      id: 'LOG-2024-005',
      timestamp: '2024-01-15 14:10:15',
      user: '系统',
      userRole: '系统',
      operation: '系统维护',
      action: '自动备份',
      target: '数据库',
      result: 'failed',
      ip: '127.0.0.1',
      userAgent: 'System',
      details: '数据库自动备份失败，错误：磁盘空间不足',
      duration: 0.5
    },
    {
      key: '6',
      id: 'LOG-2024-006',
      timestamp: '2024-01-15 14:05:28',
      user: '李工程师',
      userRole: '运维工程师',
      operation: '配置管理',
      action: '修改配置',
      target: 'SW-SH-012',
      result: 'success',
      ip: '*************',
      userAgent: 'Chrome/120.0.0.0',
      details: '修改上海交换机VLAN配置，新增VLAN 100',
      duration: 3.7
    }
  ];

  const getResultColor = (result: string) => {
    const colors = {
      success: 'green',
      failed: 'red',
      warning: 'orange'
    };
    return colors[result as keyof typeof colors] || 'default';
  };

  const getResultText = (result: string) => {
    const texts = {
      success: '成功',
      failed: '失败',
      warning: '警告'
    };
    return texts[result as keyof typeof texts] || '未知';
  };

  const getOperationColor = (operation: string) => {
    const colors = {
      '用户管理': 'blue',
      '设备管理': 'purple',
      '数据导出': 'cyan',
      '工单管理': 'orange',
      '系统维护': 'green',
      '配置管理': 'magenta'
    };
    return colors[operation as keyof typeof colors] || 'default';
  };

  const columns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 150,
      sorter: (a: any, b: any) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime(),
    },
    {
      title: '用户',
      key: 'userInfo',
      width: 120,
      render: (_, record: any) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.user}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.userRole}</div>
        </div>
      ),
    },
    {
      title: '操作',
      key: 'operationInfo',
      width: 150,
      render: (_, record: any) => (
        <div>
          <Tag color={getOperationColor(record.operation)}>{record.operation}</Tag>
          <div style={{ fontSize: '12px', marginTop: 2 }}>{record.action}</div>
        </div>
      ),
    },
    {
      title: '目标',
      dataIndex: 'target',
      key: 'target',
      width: 120,
      ellipsis: true,
    },
    {
      title: '结果',
      dataIndex: 'result',
      key: 'result',
      width: 80,
      render: (result: string) => (
        <Tag color={getResultColor(result)}>
          {getResultText(result)}
        </Tag>
      ),
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
      width: 120,
    },
    {
      title: '耗时(秒)',
      dataIndex: 'duration',
      key: 'duration',
      width: 100,
      render: (duration: number) => duration.toFixed(1),
      sorter: (a: any, b: any) => a.duration - b.duration,
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record: any) => (
        <Button
          type="text"
          icon={<EyeOutlined />}
          onClick={() => showLogDetail(record)}
        >
          详情
        </Button>
      ),
    }
  ];

  const showLogDetail = (log: any) => {
    setSelectedLog(log);
    setDetailModalVisible(true);
  };

  const handleExport = () => {
    message.success('日志导出成功');
  };

  const handleRefresh = () => {
    message.success('日志已刷新');
  };

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Alert
            message="操作日志"
            description="记录系统所有用户操作，提供审计追踪和安全监控"
            type="info"
            showIcon
          />
        </Col>
      </Row>

      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="日志总数"
              value={logStats.totalLogs}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="今日日志"
              value={logStats.todayLogs}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="成功率"
              value={logStats.successRate}
              suffix="%"
              precision={1}
              prefix={<SafetyOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="错误日志"
              value={logStats.errorLogs}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      <Card
        title="操作日志"
        extra={
          <Space>
            <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
              刷新
            </Button>
            <Button icon={<DownloadOutlined />} onClick={handleExport}>
              导出
            </Button>
          </Space>
        }
      >
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={6}>
            <Select
              placeholder="操作类型"
              style={{ width: '100%' }}
              value={filters.operation}
              onChange={(value) => setFilters({ ...filters, operation: value })}
            >
              <Option value="all">全部操作</Option>
              <Option value="用户管理">用户管理</Option>
              <Option value="设备管理">设备管理</Option>
              <Option value="数据导出">数据导出</Option>
              <Option value="工单管理">工单管理</Option>
              <Option value="系统维护">系统维护</Option>
              <Option value="配置管理">配置管理</Option>
            </Select>
          </Col>
          <Col xs={24} sm={6}>
            <Select
              placeholder="操作用户"
              style={{ width: '100%' }}
              value={filters.user}
              onChange={(value) => setFilters({ ...filters, user: value })}
            >
              <Option value="all">全部用户</Option>
              <Option value="张管理员">张管理员</Option>
              <Option value="王运维">王运维</Option>
              <Option value="刘分析师">刘分析师</Option>
              <Option value="陈客服">陈客服</Option>
              <Option value="李工程师">李工程师</Option>
            </Select>
          </Col>
          <Col xs={24} sm={6}>
            <Select
              placeholder="操作结果"
              style={{ width: '100%' }}
              value={filters.result}
              onChange={(value) => setFilters({ ...filters, result: value })}
            >
              <Option value="all">全部结果</Option>
              <Option value="success">成功</Option>
              <Option value="failed">失败</Option>
              <Option value="warning">警告</Option>
            </Select>
          </Col>
          <Col xs={24} sm={6}>
            <RangePicker
              style={{ width: '100%' }}
              placeholder={['开始时间', '结束时间']}
              onChange={(dates) => setFilters({ ...filters, dateRange: dates })}
            />
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={operationLogs}
          pagination={{
            total: operationLogs.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      <Modal
        title="操作日志详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedLog && (
          <div>
            <Descriptions column={2} bordered>
              <Descriptions.Item label="日志ID">{(selectedLog as any).id}</Descriptions.Item>
              <Descriptions.Item label="操作时间">{(selectedLog as any).timestamp}</Descriptions.Item>
              <Descriptions.Item label="操作用户">{(selectedLog as any).user}</Descriptions.Item>
              <Descriptions.Item label="用户角色">{(selectedLog as any).userRole}</Descriptions.Item>
              <Descriptions.Item label="操作类型">{(selectedLog as any).operation}</Descriptions.Item>
              <Descriptions.Item label="具体操作">{(selectedLog as any).action}</Descriptions.Item>
              <Descriptions.Item label="操作目标">{(selectedLog as any).target}</Descriptions.Item>
              <Descriptions.Item label="操作结果">
                <Tag color={getResultColor((selectedLog as any).result)}>
                  {getResultText((selectedLog as any).result)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="IP地址">{(selectedLog as any).ip}</Descriptions.Item>
              <Descriptions.Item label="用户代理">{(selectedLog as any).userAgent}</Descriptions.Item>
              <Descriptions.Item label="耗时">{(selectedLog as any).duration} 秒</Descriptions.Item>
              <Descriptions.Item label="操作详情" span={2}>
                {(selectedLog as any).details}
              </Descriptions.Item>
            </Descriptions>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default OperationLog;
