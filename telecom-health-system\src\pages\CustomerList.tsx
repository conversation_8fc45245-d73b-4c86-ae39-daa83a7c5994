import React, { useState } from 'react';
import {
  Table,
  Card,
  Button,
  Input,
  Space,
  Tag,
  Avatar,
  Tooltip,
  Modal,
  Form,
  Select,
  DatePicker,
  message
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  UserOutlined
} from '@ant-design/icons';

const { Search } = Input;
const { Option } = Select;

const CustomerList: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState(null);
  const [form] = Form.useForm();

  // 模拟客户数据
  const [customers, setCustomers] = useState([
    {
      key: '1',
      id: 'CUS001',
      name: '中国移动北京分公司',
      type: '运营商',
      level: 'VIP',
      contact: '张经理',
      phone: '138****8888',
      email: '<EMAIL>',
      status: 'active',
      createTime: '2023-01-15',
      lastActivity: '2024-01-15 14:30'
    },
    {
      key: '2',
      id: 'CUS002',
      name: '联通上海数据中心',
      type: '数据中心',
      level: '企业',
      contact: '李主任',
      phone: '139****9999',
      email: '<EMAIL>',
      status: 'active',
      createTime: '2023-03-20',
      lastActivity: '2024-01-15 13:45'
    },
    {
      key: '3',
      id: 'CUS003',
      name: '电信广州核心网',
      type: '核心网',
      level: 'VIP',
      contact: '王工程师',
      phone: '137****7777',
      email: '<EMAIL>',
      status: 'maintenance',
      createTime: '2023-05-10',
      lastActivity: '2024-01-15 12:20'
    },
    {
      key: '4',
      id: 'CUS004',
      name: '移动深圳基站群',
      type: '基站',
      level: '标准',
      contact: '刘技术员',
      phone: '136****6666',
      email: '<EMAIL>',
      status: 'inactive',
      createTime: '2023-08-25',
      lastActivity: '2024-01-14 16:30'
    }
  ]);

  const getStatusColor = (status: string) => {
    const colors = {
      active: 'green',
      maintenance: 'orange',
      inactive: 'red'
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getStatusText = (status: string) => {
    const texts = {
      active: '正常',
      maintenance: '维护中',
      inactive: '停用'
    };
    return texts[status as keyof typeof texts] || '未知';
  };

  const getLevelColor = (level: string) => {
    const colors = {
      VIP: 'gold',
      '企业': 'blue',
      '标准': 'default'
    };
    return colors[level as keyof typeof colors] || 'default';
  };

  const columns = [
    {
      title: '客户ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
    },
    {
      title: '客户名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <Space>
          <Avatar icon={<UserOutlined />} />
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '客户类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
    },
    {
      title: '客户等级',
      dataIndex: 'level',
      key: 'level',
      width: 100,
      render: (level: string) => (
        <Tag color={getLevelColor(level)}>{level}</Tag>
      ),
    },
    {
      title: '联系人',
      dataIndex: 'contact',
      key: 'contact',
      width: 100,
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      key: 'phone',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '最后活动',
      dataIndex: 'lastActivity',
      key: 'lastActivity',
      width: 150,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record: any) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleView(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleView = (record: any) => {
    message.info(`查看客户：${record.name}`);
  };

  const handleEdit = (record: any) => {
    setEditingCustomer(record);
    form.setFieldsValue(record);
    setIsModalVisible(true);
  };

  const handleDelete = (record: any) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除客户 "${record.name}" 吗？`,
      onOk() {
        setCustomers(customers.filter(item => item.key !== record.key));
        message.success('删除成功');
      },
    });
  };

  const handleAdd = () => {
    setEditingCustomer(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      if (editingCustomer) {
        // 编辑
        setCustomers(customers.map(item => 
          item.key === (editingCustomer as any).key 
            ? { ...item, ...values }
            : item
        ));
        message.success('更新成功');
      } else {
        // 新增
        const newCustomer = {
          key: Date.now().toString(),
          id: `CUS${String(customers.length + 1).padStart(3, '0')}`,
          ...values,
          createTime: new Date().toISOString().split('T')[0],
          lastActivity: new Date().toLocaleString('zh-CN')
        };
        setCustomers([...customers, newCustomer]);
        message.success('添加成功');
      }
      setIsModalVisible(false);
    });
  };

  const handleSearch = (value: string) => {
    message.info(`搜索：${value}`);
  };

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <Space>
            <Search
              placeholder="搜索客户名称、ID或联系人"
              allowClear
              enterButton={<SearchOutlined />}
              size="middle"
              onSearch={handleSearch}
              style={{ width: 300 }}
            />
          </Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            新增客户
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={customers}
          loading={loading}
          pagination={{
            total: customers.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      <Modal
        title={editingCustomer ? '编辑客户' : '新增客户'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={() => setIsModalVisible(false)}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: 'active',
            level: '标准',
            type: '企业'
          }}
        >
          <Form.Item
            name="name"
            label="客户名称"
            rules={[{ required: true, message: '请输入客户名称' }]}
          >
            <Input placeholder="请输入客户名称" />
          </Form.Item>

          <Form.Item
            name="type"
            label="客户类型"
            rules={[{ required: true, message: '请选择客户类型' }]}
          >
            <Select placeholder="请选择客户类型">
              <Option value="运营商">运营商</Option>
              <Option value="数据中心">数据中心</Option>
              <Option value="核心网">核心网</Option>
              <Option value="基站">基站</Option>
              <Option value="企业">企业</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="level"
            label="客户等级"
            rules={[{ required: true, message: '请选择客户等级' }]}
          >
            <Select placeholder="请选择客户等级">
              <Option value="VIP">VIP</Option>
              <Option value="企业">企业</Option>
              <Option value="标准">标准</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="contact"
            label="联系人"
            rules={[{ required: true, message: '请输入联系人' }]}
          >
            <Input placeholder="请输入联系人" />
          </Form.Item>

          <Form.Item
            name="phone"
            label="联系电话"
            rules={[{ required: true, message: '请输入联系电话' }]}
          >
            <Input placeholder="请输入联系电话" />
          </Form.Item>

          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input placeholder="请输入邮箱" />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select placeholder="请选择状态">
              <Option value="active">正常</Option>
              <Option value="maintenance">维护中</Option>
              <Option value="inactive">停用</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CustomerList;
