import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import MainLayout from './components/Layout/MainLayout';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import CustomerList from './pages/CustomerList';
import CustomerProfile from './pages/CustomerProfile';
import CustomerCategory from './pages/CustomerCategory';
import NetworkStatus from './pages/NetworkStatus';
import DeviceMonitor from './pages/DeviceMonitor';
import AlertManagement from './pages/AlertManagement';
import NetworkTopology from './pages/NetworkTopology';
import HealthOverview from './pages/HealthOverview';
import PerformanceMetrics from './pages/PerformanceMetrics';
import QualityAnalysis from './pages/QualityAnalysis';
import PredictionAnalysis from './pages/PredictionAnalysis';
import BusinessReports from './pages/BusinessReports';
import StatisticsAnalysis from './pages/StatisticsAnalysis';
import DataExport from './pages/DataExport';
import MaintenanceSchedule from './pages/MaintenanceSchedule';
import TicketManagement from './pages/TicketManagement';
import UserManagement from './pages/UserManagement';
import SystemConfig from './pages/SystemConfig';
import './App.css';

function App() {
  return (
    <ConfigProvider>
      <AuthProvider>
        <Router>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            <Route
              path="/*"
              element={
                <ProtectedRoute>
                  <MainLayout />
                </ProtectedRoute>
              }
            >
              <Route index element={<Navigate to="dashboard" replace />} />
              <Route path="dashboard" element={<Dashboard />} />
              <Route path="customer/list" element={<CustomerList />} />
              <Route path="customer/profile" element={<CustomerProfile />} />
              <Route path="customer/category" element={<CustomerCategory />} />
              <Route path="network/status" element={<NetworkStatus />} />
              <Route path="network/devices" element={<DeviceMonitor />} />
              <Route path="network/alerts" element={<AlertManagement />} />
              <Route path="network/topology" element={<NetworkTopology />} />
              <Route path="health/overview" element={<HealthOverview />} />
              <Route path="health/performance" element={<PerformanceMetrics />} />
              <Route path="health/quality" element={<QualityAnalysis />} />
              <Route path="health/prediction" element={<PredictionAnalysis />} />
              <Route path="reports/business" element={<BusinessReports />} />
              <Route path="reports/statistics" element={<StatisticsAnalysis />} />
              <Route path="reports/export" element={<DataExport />} />
              <Route path="maintenance/schedule" element={<MaintenanceSchedule />} />
              <Route path="maintenance/tickets" element={<TicketManagement />} />
              <Route path="system/users" element={<UserManagement />} />
              <Route path="system/config" element={<SystemConfig />} />
            </Route>
          </Routes>
        </Router>
      </AuthProvider>
    </ConfigProvider>
  );
}

export default App;
