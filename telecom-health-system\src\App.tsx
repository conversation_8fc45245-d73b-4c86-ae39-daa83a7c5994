import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, Spin } from 'antd';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import MainLayout from './components/Layout/MainLayout';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import './App.css';

// 动态导入页面组件
const CustomerList = React.lazy(() => import('./pages/CustomerList'));
const CustomerProfile = React.lazy(() => import('./pages/CustomerProfile'));
const CustomerCategory = React.lazy(() => import('./pages/CustomerCategory'));
const NetworkStatus = React.lazy(() => import('./pages/NetworkStatus'));
const DeviceMonitor = React.lazy(() => import('./pages/DeviceMonitor'));
const AlertManagement = React.lazy(() => import('./pages/AlertManagement'));
const NetworkTopology = React.lazy(() => import('./pages/NetworkTopology'));
const HealthOverview = React.lazy(() => import('./pages/HealthOverview'));
const PerformanceMetrics = React.lazy(() => import('./pages/PerformanceMetrics'));
const QualityAnalysis = React.lazy(() => import('./pages/QualityAnalysis'));
const PredictionAnalysis = React.lazy(() => import('./pages/PredictionAnalysisNew'));
const BusinessReports = React.lazy(() => import('./pages/BusinessReports'));
const StatisticsAnalysis = React.lazy(() => import('./pages/StatisticsAnalysis'));
const DataExport = React.lazy(() => import('./pages/DataExport'));
const MaintenanceSchedule = React.lazy(() => import('./pages/MaintenanceSchedule'));
const TicketManagement = React.lazy(() => import('./pages/TicketManagement'));
const UserManagement = React.lazy(() => import('./pages/UserManagement'));
const SystemConfig = React.lazy(() => import('./pages/SystemConfig'));

function App() {
  return (
    <ConfigProvider>
      <AuthProvider>
        <Router>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            <Route
              path="/*"
              element={
                <ProtectedRoute>
                  <MainLayout />
                </ProtectedRoute>
              }
            >
              <Route index element={<Navigate to="dashboard" replace />} />
              <Route path="dashboard" element={<Dashboard />} />

              {/* 客户管理模块 */}
              <Route path="customer/list" element={
                <Suspense fallback={<Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }} />}>
                  <CustomerList />
                </Suspense>
              } />
              <Route path="customer/profile" element={
                <Suspense fallback={<Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }} />}>
                  <CustomerProfile />
                </Suspense>
              } />
              <Route path="customer/category" element={
                <Suspense fallback={<Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }} />}>
                  <CustomerCategory />
                </Suspense>
              } />

              {/* 网络监控模块 */}
              <Route path="network/status" element={
                <Suspense fallback={<Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }} />}>
                  <NetworkStatus />
                </Suspense>
              } />
              <Route path="network/devices" element={
                <Suspense fallback={<Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }} />}>
                  <DeviceMonitor />
                </Suspense>
              } />
              <Route path="network/alerts" element={
                <Suspense fallback={<Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }} />}>
                  <AlertManagement />
                </Suspense>
              } />
              <Route path="network/topology" element={
                <Suspense fallback={<Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }} />}>
                  <NetworkTopology />
                </Suspense>
              } />

              {/* 健康评估模块 */}
              <Route path="health/overview" element={
                <Suspense fallback={<Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }} />}>
                  <HealthOverview />
                </Suspense>
              } />
              <Route path="health/performance" element={
                <Suspense fallback={<Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }} />}>
                  <PerformanceMetrics />
                </Suspense>
              } />
              <Route path="health/quality" element={
                <Suspense fallback={<Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }} />}>
                  <QualityAnalysis />
                </Suspense>
              } />
              <Route path="health/prediction" element={
                <Suspense fallback={<Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }} />}>
                  <PredictionAnalysis />
                </Suspense>
              } />

              {/* 报表统计模块 */}
              <Route path="reports/business" element={
                <Suspense fallback={<Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }} />}>
                  <BusinessReports />
                </Suspense>
              } />
              <Route path="reports/statistics" element={
                <Suspense fallback={<Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }} />}>
                  <StatisticsAnalysis />
                </Suspense>
              } />
              <Route path="reports/export" element={
                <Suspense fallback={<Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }} />}>
                  <DataExport />
                </Suspense>
              } />

              {/* 运维管理模块 */}
              <Route path="maintenance/schedule" element={
                <Suspense fallback={<Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }} />}>
                  <MaintenanceSchedule />
                </Suspense>
              } />
              <Route path="maintenance/tickets" element={
                <Suspense fallback={<Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }} />}>
                  <TicketManagement />
                </Suspense>
              } />

              {/* 系统管理模块 */}
              <Route path="system/users" element={
                <Suspense fallback={<Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }} />}>
                  <UserManagement />
                </Suspense>
              } />
              <Route path="system/config" element={
                <Suspense fallback={<Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }} />}>
                  <SystemConfig />
                </Suspense>
              } />
            </Route>
          </Routes>
        </Router>
      </AuthProvider>
    </ConfigProvider>
  );
}

export default App;
