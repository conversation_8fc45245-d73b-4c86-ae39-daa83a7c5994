import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import MainLayout from './components/Layout/MainLayout';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import StatisticsFinal from './pages/StatisticsFinal';
import DataExportSimple from './pages/DataExportSimple';
import MaintenanceScheduleSimple from './pages/MaintenanceScheduleSimple';
import TicketManagementSimple from './pages/TicketManagementSimple';
import UserManagementSimple from './pages/UserManagementSimple';
import SystemConfigSimple from './pages/SystemConfigSimple';
import KnowledgeTest from './pages/KnowledgeTest';
import PermissionTest from './pages/PermissionTest';
import OperationLog from './pages/OperationLog';
import './App.css';

function App() {
  return (
    <ConfigProvider>
      <AuthProvider>
        <Router>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            <Route
              path="/*"
              element={
                <ProtectedRoute>
                  <MainLayout />
                </ProtectedRoute>
              }
            >
              <Route index element={<Navigate to="dashboard" replace />} />
              <Route path="dashboard" element={<Dashboard />} />
              <Route path="reports/statistics" element={<StatisticsFinal />} />
              <Route path="reports/export" element={<DataExportSimple />} />
              <Route path="maintenance/schedule" element={<MaintenanceScheduleSimple />} />
              <Route path="maintenance/tickets" element={<TicketManagementSimple />} />
              <Route path="system/users" element={<UserManagementSimple />} />
              <Route path="system/permissions" element={<PermissionTest />} />
              <Route path="system/logs" element={<OperationLog />} />
              <Route path="system/knowledge" element={<KnowledgeTest />} />
              <Route path="system/config" element={<SystemConfigSimple />} />
            </Route>
          </Routes>
        </Router>
      </AuthProvider>
    </ConfigProvider>
  );
}

export default App;
