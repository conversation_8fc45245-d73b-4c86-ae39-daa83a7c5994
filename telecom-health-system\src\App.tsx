import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import MainLayout from './components/Layout/MainLayout';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import CustomerList from './pages/CustomerList';
import CustomerProfile from './pages/CustomerProfile';
import CustomerCategory from './pages/CustomerCategory';
import NetworkStatus from './pages/NetworkStatus';
import DeviceMonitor from './pages/DeviceMonitor';
import AlertManagement from './pages/AlertManagement';
import NetworkTopology from './pages/NetworkTopology';
import HealthOverview from './pages/HealthOverview';
import './App.css';

function App() {
  return (
    <ConfigProvider>
      <AuthProvider>
        <Router>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            <Route
              path="/*"
              element={
                <ProtectedRoute>
                  <MainLayout />
                </ProtectedRoute>
              }
            >
              <Route index element={<Navigate to="dashboard" replace />} />
              <Route path="dashboard" element={<Dashboard />} />
              <Route path="customer/list" element={<CustomerList />} />
              <Route path="customer/profile" element={<CustomerProfile />} />
              <Route path="customer/category" element={<CustomerCategory />} />
              <Route path="network/status" element={<NetworkStatus />} />
              <Route path="network/devices" element={<DeviceMonitor />} />
              <Route path="network/alerts" element={<AlertManagement />} />
              <Route path="network/topology" element={<NetworkTopology />} />
              <Route path="health/overview" element={<HealthOverview />} />
            </Route>
          </Routes>
        </Router>
      </AuthProvider>
    </ConfigProvider>
  );
}

export default App;
