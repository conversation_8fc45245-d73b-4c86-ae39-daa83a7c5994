import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Select,
  Tooltip,
  Badge,
  Tag,
  Modal,
  Descriptions,
  Alert,
  Statistic,
  Switch
} from 'antd';
import {
  ZoomInOutlined,
  ZoomOutOutlined,
  ReloadOutlined,
  FullscreenOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  WifiOutlined,
  DatabaseOutlined,
  SafetyOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';

const { Option } = Select;

const NetworkTopology: React.FC = () => {
  const [selectedRegion, setSelectedRegion] = useState('all');
  const [showLabels, setShowLabels] = useState(true);
  const [showTraffic, setShowTraffic] = useState(true);
  const [selectedNode, setSelectedNode] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);

  // 网络节点数据
  const networkNodes = [
    {
      id: 'core-bj-01',
      name: '北京核心路由器-01',
      type: 'core-router',
      status: 'online',
      x: 400,
      y: 200,
      region: '北京',
      connections: ['edge-bj-01', 'edge-bj-02', 'core-sh-01'],
      traffic: 85,
      cpu: 45,
      memory: 67,
      description: '北京地区核心路由器，负责区域间流量转发'
    },
    {
      id: 'core-sh-01',
      name: '上海核心路由器-01',
      type: 'core-router',
      status: 'online',
      x: 600,
      y: 300,
      region: '上海',
      connections: ['core-bj-01', 'edge-sh-01', 'edge-sh-02', 'core-gz-01'],
      traffic: 92,
      cpu: 52,
      memory: 74,
      description: '上海地区核心路由器，华东地区流量汇聚点'
    },
    {
      id: 'core-gz-01',
      name: '广州核心路由器-01',
      type: 'core-router',
      status: 'warning',
      x: 500,
      y: 450,
      region: '广州',
      connections: ['core-sh-01', 'edge-gz-01', 'edge-sz-01'],
      traffic: 78,
      cpu: 88,
      memory: 91,
      description: '广州地区核心路由器，华南地区流量汇聚点'
    },
    {
      id: 'edge-bj-01',
      name: '北京边缘路由器-01',
      type: 'edge-router',
      status: 'online',
      x: 300,
      y: 150,
      region: '北京',
      connections: ['core-bj-01', 'sw-bj-01', 'sw-bj-02'],
      traffic: 65,
      cpu: 35,
      memory: 45,
      description: '北京地区边缘路由器，接入层设备'
    },
    {
      id: 'edge-bj-02',
      name: '北京边缘路由器-02',
      type: 'edge-router',
      status: 'online',
      x: 500,
      y: 150,
      region: '北京',
      connections: ['core-bj-01', 'sw-bj-03'],
      traffic: 58,
      cpu: 42,
      memory: 38,
      description: '北京地区边缘路由器，备用链路'
    },
    {
      id: 'edge-sh-01',
      name: '上海边缘路由器-01',
      type: 'edge-router',
      status: 'online',
      x: 550,
      y: 250,
      region: '上海',
      connections: ['core-sh-01', 'sw-sh-01'],
      traffic: 72,
      cpu: 48,
      memory: 55,
      description: '上海地区边缘路由器'
    },
    {
      id: 'edge-sh-02',
      name: '上海边缘路由器-02',
      type: 'edge-router',
      status: 'online',
      x: 650,
      y: 250,
      region: '上海',
      connections: ['core-sh-01', 'sw-sh-02'],
      traffic: 69,
      cpu: 41,
      memory: 52,
      description: '上海地区边缘路由器'
    },
    {
      id: 'edge-gz-01',
      name: '广州边缘路由器-01',
      type: 'edge-router',
      status: 'online',
      x: 450,
      y: 400,
      region: '广州',
      connections: ['core-gz-01', 'sw-gz-01'],
      traffic: 61,
      cpu: 39,
      memory: 47,
      description: '广州地区边缘路由器'
    },
    {
      id: 'edge-sz-01',
      name: '深圳边缘路由器-01',
      type: 'edge-router',
      status: 'offline',
      x: 550,
      y: 400,
      region: '深圳',
      connections: ['core-gz-01', 'sw-sz-01'],
      traffic: 0,
      cpu: 0,
      memory: 0,
      description: '深圳地区边缘路由器，当前离线'
    },
    {
      id: 'fw-bj-01',
      name: '北京防火墙-01',
      type: 'firewall',
      status: 'online',
      x: 200,
      y: 200,
      region: '北京',
      connections: ['edge-bj-01'],
      traffic: 45,
      cpu: 32,
      memory: 41,
      description: '北京地区安全防护设备'
    },
    {
      id: 'lb-sh-01',
      name: '上海负载均衡器-01',
      type: 'load-balancer',
      status: 'online',
      x: 700,
      y: 350,
      region: '上海',
      connections: ['edge-sh-02'],
      traffic: 83,
      cpu: 56,
      memory: 62,
      description: '上海地区负载均衡设备'
    }
  ];

  const getNodeColor = (node: any) => {
    if (node.status === 'offline') return '#ff4d4f';
    if (node.status === 'warning') return '#faad14';
    if (node.type === 'core-router') return '#1890ff';
    if (node.type === 'edge-router') return '#52c41a';
    if (node.type === 'firewall') return '#722ed1';
    if (node.type === 'load-balancer') return '#eb2f96';
    return '#13c2c2';
  };

  const getNodeIcon = (type: string) => {
    const icons = {
      'core-router': <DatabaseOutlined />,
      'edge-router': <WifiOutlined />,
      'firewall': <SafetyOutlined />,
      'load-balancer': <ThunderboltOutlined />
    };
    return icons[type as keyof typeof icons] || <DatabaseOutlined />;
  };

  const getNodeSize = (type: string) => {
    const sizes = {
      'core-router': 40,
      'edge-router': 30,
      'firewall': 25,
      'load-balancer': 25
    };
    return sizes[type as keyof typeof sizes] || 30;
  };

  const handleNodeClick = (node: any) => {
    setSelectedNode(node);
    setDetailModalVisible(true);
  };

  const renderNode = (node: any) => {
    const size = getNodeSize(node.type);
    const color = getNodeColor(node);
    
    return (
      <g key={node.id}>
        {/* 节点圆圈 */}
        <circle
          cx={node.x}
          cy={node.y}
          r={size / 2}
          fill={color}
          stroke="#fff"
          strokeWidth="2"
          style={{ cursor: 'pointer' }}
          onClick={() => handleNodeClick(node)}
        />
        
        {/* 节点图标 */}
        <foreignObject
          x={node.x - 8}
          y={node.y - 8}
          width="16"
          height="16"
          style={{ pointerEvents: 'none' }}
        >
          <div style={{ color: 'white', fontSize: '12px', textAlign: 'center' }}>
            {getNodeIcon(node.type)}
          </div>
        </foreignObject>
        
        {/* 节点标签 */}
        {showLabels && (
          <text
            x={node.x}
            y={node.y + size / 2 + 15}
            textAnchor="middle"
            fontSize="10"
            fill="#666"
            style={{ pointerEvents: 'none' }}
          >
            {node.name.split('-').pop()}
          </text>
        )}
        
        {/* 流量指示器 */}
        {showTraffic && node.traffic > 0 && (
          <circle
            cx={node.x + size / 2 - 5}
            cy={node.y - size / 2 + 5}
            r="3"
            fill={node.traffic > 80 ? '#ff4d4f' : node.traffic > 60 ? '#faad14' : '#52c41a'}
          />
        )}
      </g>
    );
  };

  const renderConnection = (fromNode: any, toNode: any) => {
    const trafficWidth = Math.max(1, (fromNode.traffic + toNode.traffic) / 50);
    const color = fromNode.status === 'offline' || toNode.status === 'offline' ? '#ff4d4f' : '#d9d9d9';
    
    return (
      <line
        key={`${fromNode.id}-${toNode.id}`}
        x1={fromNode.x}
        y1={fromNode.y}
        x2={toNode.x}
        y2={toNode.y}
        stroke={color}
        strokeWidth={trafficWidth}
        opacity="0.6"
      />
    );
  };

  const renderConnections = () => {
    const connections: JSX.Element[] = [];
    
    networkNodes.forEach(node => {
      node.connections.forEach(connId => {
        const targetNode = networkNodes.find(n => n.id === connId);
        if (targetNode) {
          connections.push(renderConnection(node, targetNode));
        }
      });
    });
    
    return connections;
  };

  const filteredNodes = selectedRegion === 'all' 
    ? networkNodes 
    : networkNodes.filter(node => node.region === selectedRegion);

  // 统计数据
  const stats = {
    totalNodes: networkNodes.length,
    onlineNodes: networkNodes.filter(n => n.status === 'online').length,
    warningNodes: networkNodes.filter(n => n.status === 'warning').length,
    offlineNodes: networkNodes.filter(n => n.status === 'offline').length
  };

  return (
    <div>
      {/* 控制面板 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Alert
            message="网络拓扑图"
            description="实时显示网络设备连接关系和状态信息"
            type="info"
            showIcon
          />
        </Col>
      </Row>

      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col xs={24} sm={6}>
          <Card size="small">
            <Statistic
              title="设备总数"
              value={stats.totalNodes}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card size="small">
            <Statistic
              title="在线设备"
              value={stats.onlineNodes}
              prefix={<WifiOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card size="small">
            <Statistic
              title="告警设备"
              value={stats.warningNodes}
              prefix={<InfoCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card size="small">
            <Statistic
              title="离线设备"
              value={stats.offlineNodes}
              prefix={<InfoCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 拓扑图 */}
      <Card
        title="网络拓扑图"
        extra={
          <Space>
            <Select
              value={selectedRegion}
              onChange={setSelectedRegion}
              style={{ width: 120 }}
            >
              <Option value="all">全部区域</Option>
              <Option value="北京">北京</Option>
              <Option value="上海">上海</Option>
              <Option value="广州">广州</Option>
              <Option value="深圳">深圳</Option>
            </Select>
            <Tooltip title="显示标签">
              <Switch
                checked={showLabels}
                onChange={setShowLabels}
                checkedChildren="标签"
                unCheckedChildren="标签"
              />
            </Tooltip>
            <Tooltip title="显示流量">
              <Switch
                checked={showTraffic}
                onChange={setShowTraffic}
                checkedChildren="流量"
                unCheckedChildren="流量"
              />
            </Tooltip>
            <Button icon={<ZoomInOutlined />} />
            <Button icon={<ZoomOutOutlined />} />
            <Button icon={<FullscreenOutlined />} />
            <Button icon={<ReloadOutlined />} />
          </Space>
        }
      >
        <div style={{ height: '600px', border: '1px solid #d9d9d9', borderRadius: '6px', overflow: 'hidden' }}>
          <svg width="100%" height="100%" viewBox="0 0 900 600">
            {/* 渲染连接线 */}
            {renderConnections()}
            
            {/* 渲染节点 */}
            {filteredNodes.map(node => renderNode(node))}
          </svg>
        </div>
        
        {/* 图例 */}
        <div style={{ marginTop: 16, padding: '16px', backgroundColor: '#fafafa', borderRadius: '6px' }}>
          <Row gutter={16}>
            <Col span={6}>
              <Space>
                <Badge color="#1890ff" />
                <span>核心路由器</span>
              </Space>
            </Col>
            <Col span={6}>
              <Space>
                <Badge color="#52c41a" />
                <span>边缘路由器</span>
              </Space>
            </Col>
            <Col span={6}>
              <Space>
                <Badge color="#722ed1" />
                <span>防火墙</span>
              </Space>
            </Col>
            <Col span={6}>
              <Space>
                <Badge color="#eb2f96" />
                <span>负载均衡器</span>
              </Space>
            </Col>
          </Row>
          <Row gutter={16} style={{ marginTop: 8 }}>
            <Col span={6}>
              <Space>
                <Badge status="success" />
                <span>在线</span>
              </Space>
            </Col>
            <Col span={6}>
              <Space>
                <Badge status="warning" />
                <span>告警</span>
              </Space>
            </Col>
            <Col span={6}>
              <Space>
                <Badge status="error" />
                <span>离线</span>
              </Space>
            </Col>
            <Col span={6}>
              <Space>
                <div style={{ width: 8, height: 8, borderRadius: '50%', backgroundColor: '#52c41a', display: 'inline-block' }}></div>
                <span>流量指示器</span>
              </Space>
            </Col>
          </Row>
        </div>
      </Card>

      {/* 节点详情模态框 */}
      <Modal
        title="设备详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedNode && (
          <div>
            <Descriptions column={2} bordered>
              <Descriptions.Item label="设备ID">{(selectedNode as any).id}</Descriptions.Item>
              <Descriptions.Item label="设备名称">{(selectedNode as any).name}</Descriptions.Item>
              <Descriptions.Item label="设备类型">{(selectedNode as any).type}</Descriptions.Item>
              <Descriptions.Item label="所在区域">{(selectedNode as any).region}</Descriptions.Item>
              <Descriptions.Item label="运行状态">
                <Tag color={getNodeColor(selectedNode)}>
                  {(selectedNode as any).status === 'online' ? '在线' : 
                   (selectedNode as any).status === 'warning' ? '告警' : '离线'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="流量使用率">{(selectedNode as any).traffic}%</Descriptions.Item>
              <Descriptions.Item label="CPU使用率">{(selectedNode as any).cpu}%</Descriptions.Item>
              <Descriptions.Item label="内存使用率">{(selectedNode as any).memory}%</Descriptions.Item>
              <Descriptions.Item label="连接数量">{(selectedNode as any).connections.length}</Descriptions.Item>
              <Descriptions.Item label="描述" span={2}>
                {(selectedNode as any).description}
              </Descriptions.Item>
            </Descriptions>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default NetworkTopology;
