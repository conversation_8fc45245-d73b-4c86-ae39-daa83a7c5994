import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Select,
  DatePicker,
  Button,
  Space,
  Alert,
  Progress,
  Tag,
  Tooltip,
  Tabs,
  Divider,
  Typography
} from 'antd';
import {
  FileTextOutlined,
  Bar<PERSON><PERSON>Outlined,
  Pie<PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  DollarOutlined,
  UserOutlined,
  WifiOutlined,
  TrophyOutlined,
  DownloadOutlined,
  PrinterOutlined,
  MailOutlined,
  CalendarOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const { Title, Text } = Typography;

const BusinessReports: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [selectedReport, setSelectedReport] = useState('revenue');

  // 业务概览数据
  const businessOverview = {
    totalRevenue: 28500000,
    revenueGrowth: 12.5,
    totalCustomers: 3414,
    customerGrowth: 8.3,
    serviceUptime: 99.95,
    customerSatisfaction: 4.2,
    marketShare: 15.8,
    profitMargin: 23.5
  };

  // 收入报表数据
  const revenueData = [
    {
      key: '1',
      serviceType: '5G核心网服务',
      currentMonth: 8500000,
      lastMonth: 7800000,
      growth: 8.97,
      customerCount: 156,
      avgRevenue: 54487,
      marketShare: 25.2
    },
    {
      key: '2',
      serviceType: '数据中心互联',
      currentMonth: 6200000,
      lastMonth: 5900000,
      growth: 5.08,
      customerCount: 89,
      avgRevenue: 69663,
      marketShare: 18.5
    },
    {
      key: '3',
      serviceType: '云网融合服务',
      currentMonth: 5800000,
      lastMonth: 5200000,
      growth: 11.54,
      customerCount: 234,
      avgRevenue: 24786,
      marketShare: 22.1
    },
    {
      key: '4',
      serviceType: '物联网平台',
      currentMonth: 3200000,
      lastMonth: 3100000,
      growth: 3.23,
      customerCount: 67,
      avgRevenue: 47761,
      marketShare: 12.8
    },
    {
      key: '5',
      serviceType: '企业专线',
      currentMonth: 4800000,
      lastMonth: 4500000,
      growth: 6.67,
      customerCount: 445,
      avgRevenue: 10787,
      marketShare: 21.4
    }
  ];

  // 客户分析数据
  const customerAnalysis = [
    {
      key: '1',
      customerType: 'VIP客户',
      count: 25,
      revenue: 12500000,
      avgRevenue: 500000,
      satisfaction: 4.8,
      retention: 98.5,
      growth: 15.2
    },
    {
      key: '2',
      customerType: '企业客户',
      count: 156,
      revenue: 9800000,
      avgRevenue: 62821,
      satisfaction: 4.3,
      retention: 94.2,
      growth: 12.8
    },
    {
      key: '3',
      customerType: '标准客户',
      count: 892,
      revenue: 4200000,
      avgRevenue: 4708,
      satisfaction: 4.0,
      retention: 89.5,
      growth: 8.5
    },
    {
      key: '4',
      customerType: '基础客户',
      count: 2341,
      revenue: 2000000,
      avgRevenue: 854,
      satisfaction: 3.8,
      retention: 85.2,
      growth: 5.2
    }
  ];

  // 服务质量报表
  const serviceQualityData = [
    {
      key: '1',
      serviceName: '5G核心网服务',
      slaTarget: 99.9,
      actualUptime: 99.98,
      slaCompliance: 100,
      incidentCount: 2,
      mttr: 15,
      customerSatisfaction: 4.6
    },
    {
      key: '2',
      serviceName: '数据中心互联',
      slaTarget: 99.95,
      actualUptime: 99.92,
      slaCompliance: 99.7,
      incidentCount: 3,
      mttr: 25,
      customerSatisfaction: 4.3
    },
    {
      key: '3',
      serviceName: '云网融合服务',
      slaTarget: 99.5,
      actualUptime: 99.85,
      slaCompliance: 100,
      incidentCount: 8,
      mttr: 45,
      customerSatisfaction: 4.1
    },
    {
      key: '4',
      serviceName: '物联网平台',
      slaTarget: 99.0,
      actualUptime: 99.12,
      slaCompliance: 100,
      incidentCount: 15,
      mttr: 65,
      customerSatisfaction: 3.8
    }
  ];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getGrowthColor = (growth: number) => {
    return growth > 0 ? '#52c41a' : growth < 0 ? '#ff4d4f' : '#1890ff';
  };

  const getGrowthIcon = (growth: number) => {
    return growth > 0 ? '↗' : growth < 0 ? '↘' : '→';
  };

  const revenueColumns = [
    {
      title: '服务类型',
      dataIndex: 'serviceType',
      key: 'serviceType',
      width: 150,
    },
    {
      title: '本月收入',
      dataIndex: 'currentMonth',
      key: 'currentMonth',
      width: 120,
      render: (amount: number) => formatCurrency(amount),
    },
    {
      title: '上月收入',
      dataIndex: 'lastMonth',
      key: 'lastMonth',
      width: 120,
      render: (amount: number) => formatCurrency(amount),
    },
    {
      title: '增长率',
      dataIndex: 'growth',
      key: 'growth',
      width: 100,
      render: (growth: number) => (
        <span style={{ color: getGrowthColor(growth) }}>
          {getGrowthIcon(growth)} {growth.toFixed(2)}%
        </span>
      ),
    },
    {
      title: '客户数量',
      dataIndex: 'customerCount',
      key: 'customerCount',
      width: 100,
    },
    {
      title: '平均收入',
      dataIndex: 'avgRevenue',
      key: 'avgRevenue',
      width: 120,
      render: (amount: number) => formatCurrency(amount),
    },
    {
      title: '市场份额',
      dataIndex: 'marketShare',
      key: 'marketShare',
      width: 120,
      render: (share: number) => (
        <Progress
          percent={share}
          size="small"
          format={(percent) => `${percent}%`}
        />
      ),
    }
  ];

  const customerColumns = [
    {
      title: '客户类型',
      dataIndex: 'customerType',
      key: 'customerType',
      width: 120,
    },
    {
      title: '客户数量',
      dataIndex: 'count',
      key: 'count',
      width: 100,
    },
    {
      title: '总收入',
      dataIndex: 'revenue',
      key: 'revenue',
      width: 120,
      render: (amount: number) => formatCurrency(amount),
    },
    {
      title: '平均收入',
      dataIndex: 'avgRevenue',
      key: 'avgRevenue',
      width: 120,
      render: (amount: number) => formatCurrency(amount),
    },
    {
      title: '满意度',
      dataIndex: 'satisfaction',
      key: 'satisfaction',
      width: 100,
      render: (score: number) => (
        <div>
          <div>{score}/5.0</div>
          <Progress
            percent={score * 20}
            size="small"
            strokeColor="#faad14"
            showInfo={false}
          />
        </div>
      ),
    },
    {
      title: '留存率',
      dataIndex: 'retention',
      key: 'retention',
      width: 100,
      render: (rate: number) => `${rate}%`,
    },
    {
      title: '增长率',
      dataIndex: 'growth',
      key: 'growth',
      width: 100,
      render: (growth: number) => (
        <span style={{ color: getGrowthColor(growth) }}>
          {getGrowthIcon(growth)} {growth.toFixed(1)}%
        </span>
      ),
    }
  ];

  const qualityColumns = [
    {
      title: '服务名称',
      dataIndex: 'serviceName',
      key: 'serviceName',
      width: 150,
    },
    {
      title: 'SLA目标',
      dataIndex: 'slaTarget',
      key: 'slaTarget',
      width: 100,
      render: (target: number) => `${target}%`,
    },
    {
      title: '实际可用性',
      dataIndex: 'actualUptime',
      key: 'actualUptime',
      width: 120,
      render: (uptime: number) => (
        <div>
          <div>{uptime}%</div>
          <Progress
            percent={uptime}
            size="small"
            strokeColor={uptime >= 99.9 ? '#52c41a' : uptime >= 99.5 ? '#1890ff' : '#faad14'}
            showInfo={false}
          />
        </div>
      ),
    },
    {
      title: 'SLA达成率',
      dataIndex: 'slaCompliance',
      key: 'slaCompliance',
      width: 120,
      render: (compliance: number) => (
        <Tag color={compliance >= 100 ? 'green' : compliance >= 95 ? 'blue' : 'orange'}>
          {compliance}%
        </Tag>
      ),
    },
    {
      title: '故障次数',
      dataIndex: 'incidentCount',
      key: 'incidentCount',
      width: 100,
    },
    {
      title: 'MTTR(分钟)',
      dataIndex: 'mttr',
      key: 'mttr',
      width: 100,
    },
    {
      title: '客户满意度',
      dataIndex: 'customerSatisfaction',
      key: 'customerSatisfaction',
      width: 120,
      render: (score: number) => `${score}/5.0`,
    }
  ];

  return (
    <div>
      {/* 控制面板 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Alert
            message="业务报表中心"
            description="提供收入分析、客户分析、服务质量等多维度业务报表"
            type="info"
            showIcon
            action={
              <Space>
                <Button icon={<CalendarOutlined />} size="small">
                  定时报表
                </Button>
                <Button icon={<MailOutlined />} size="small">
                  邮件订阅
                </Button>
              </Space>
            }
          />
        </Col>
      </Row>

      {/* 业务概览 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={3}>
          <Card>
            <Statistic
              title="总收入"
              value={businessOverview.totalRevenue}
              formatter={(value) => formatCurrency(Number(value))}
              prefix={<DollarOutlined />}
              valueStyle={{ color: '#52c41a' }}
              suffix={
                <div style={{ fontSize: '12px', color: '#52c41a' }}>
                  ↗ {businessOverview.revenueGrowth}%
                </div>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={3}>
          <Card>
            <Statistic
              title="客户总数"
              value={businessOverview.totalCustomers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
              suffix={
                <div style={{ fontSize: '12px', color: '#52c41a' }}>
                  ↗ {businessOverview.customerGrowth}%
                </div>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={3}>
          <Card>
            <Statistic
              title="服务可用性"
              value={businessOverview.serviceUptime}
              suffix="%"
              precision={2}
              prefix={<WifiOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={3}>
          <Card>
            <Statistic
              title="客户满意度"
              value={businessOverview.customerSatisfaction}
              suffix="/5.0"
              precision={1}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={3}>
          <Card>
            <Statistic
              title="市场份额"
              value={businessOverview.marketShare}
              suffix="%"
              precision={1}
              prefix={<PieChartOutlined />}
              valueStyle={{ color: '#13c2c2' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={3}>
          <Card>
            <Statistic
              title="利润率"
              value={businessOverview.profitMargin}
              suffix="%"
              precision={1}
              prefix={<BarChartOutlined />}
              valueStyle={{ color: '#eb2f96' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 详细报表 */}
      <Card>
        <Tabs 
          defaultActiveKey="revenue"
          tabBarExtraContent={
            <Space>
              <Select
                value={selectedPeriod}
                onChange={setSelectedPeriod}
                style={{ width: 120 }}
              >
                <Option value="day">日报</Option>
                <Option value="week">周报</Option>
                <Option value="month">月报</Option>
                <Option value="quarter">季报</Option>
                <Option value="year">年报</Option>
              </Select>
              <RangePicker size="small" />
              <Button icon={<DownloadOutlined />} size="small">
                导出Excel
              </Button>
              <Button icon={<PrinterOutlined />} size="small">
                打印报表
              </Button>
            </Space>
          }
        >
          <TabPane tab="收入分析" key="revenue">
            <div style={{ marginBottom: 16 }}>
              <Title level={4}>收入分析报表</Title>
              <Text type="secondary">按服务类型统计收入情况和增长趋势</Text>
            </div>
            <Table
              columns={revenueColumns}
              dataSource={revenueData}
              pagination={false}
              scroll={{ x: 800 }}
              summary={(pageData) => {
                const totalCurrent = pageData.reduce((sum, item) => sum + item.currentMonth, 0);
                const totalLast = pageData.reduce((sum, item) => sum + item.lastMonth, 0);
                const totalGrowth = ((totalCurrent - totalLast) / totalLast * 100);
                
                return (
                  <Table.Summary.Row>
                    <Table.Summary.Cell index={0}><strong>总计</strong></Table.Summary.Cell>
                    <Table.Summary.Cell index={1}>
                      <strong>{formatCurrency(totalCurrent)}</strong>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={2}>
                      <strong>{formatCurrency(totalLast)}</strong>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={3}>
                      <strong style={{ color: getGrowthColor(totalGrowth) }}>
                        {getGrowthIcon(totalGrowth)} {totalGrowth.toFixed(2)}%
                      </strong>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={4}>
                      <strong>{pageData.reduce((sum, item) => sum + item.customerCount, 0)}</strong>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={5}>
                      <strong>{formatCurrency(totalCurrent / pageData.reduce((sum, item) => sum + item.customerCount, 0))}</strong>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={6}>-</Table.Summary.Cell>
                  </Table.Summary.Row>
                );
              }}
            />
          </TabPane>

          <TabPane tab="客户分析" key="customer">
            <div style={{ marginBottom: 16 }}>
              <Title level={4}>客户分析报表</Title>
              <Text type="secondary">按客户类型统计客户数量、收入贡献和满意度</Text>
            </div>
            <Table
              columns={customerColumns}
              dataSource={customerAnalysis}
              pagination={false}
              scroll={{ x: 700 }}
            />
          </TabPane>

          <TabPane tab="服务质量" key="quality">
            <div style={{ marginBottom: 16 }}>
              <Title level={4}>服务质量报表</Title>
              <Text type="secondary">服务可用性、SLA达成率和客户满意度统计</Text>
            </div>
            <Table
              columns={qualityColumns}
              dataSource={serviceQualityData}
              pagination={false}
              scroll={{ x: 800 }}
            />
          </TabPane>

          <TabPane tab="趋势分析" key="trend">
            <div style={{ marginBottom: 16 }}>
              <Title level={4}>趋势分析</Title>
              <Text type="secondary">业务关键指标的历史趋势和预测</Text>
            </div>
            <Row gutter={16}>
              <Col span={12}>
                <Card title="收入趋势" size="small">
                  <div style={{ height: '200px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#999' }}>
                    收入趋势图表 (可集成 ECharts)
                  </div>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="客户增长趋势" size="small">
                  <div style={{ height: '200px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#999' }}>
                    客户增长趋势图表 (可集成 ECharts)
                  </div>
                </Card>
              </Col>
            </Row>
            <Row gutter={16} style={{ marginTop: 16 }}>
              <Col span={12}>
                <Card title="服务质量趋势" size="small">
                  <div style={{ height: '200px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#999' }}>
                    服务质量趋势图表 (可集成 ECharts)
                  </div>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="市场份额变化" size="small">
                  <div style={{ height: '200px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#999' }}>
                    市场份额变化图表 (可集成 ECharts)
                  </div>
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="对比分析" key="comparison">
            <div style={{ marginBottom: 16 }}>
              <Title level={4}>对比分析</Title>
              <Text type="secondary">不同时期、不同服务类型的对比分析</Text>
            </div>
            <Row gutter={16}>
              <Col span={8}>
                <Card title="本月 vs 上月" size="small">
                  <Statistic
                    title="收入增长"
                    value={businessOverview.revenueGrowth}
                    suffix="%"
                    valueStyle={{ color: '#52c41a' }}
                    prefix="↗"
                  />
                  <Divider />
                  <Statistic
                    title="客户增长"
                    value={businessOverview.customerGrowth}
                    suffix="%"
                    valueStyle={{ color: '#1890ff' }}
                    prefix="↗"
                  />
                </Card>
              </Col>
              <Col span={8}>
                <Card title="本季度 vs 上季度" size="small">
                  <Statistic
                    title="收入增长"
                    value={18.5}
                    suffix="%"
                    valueStyle={{ color: '#52c41a' }}
                    prefix="↗"
                  />
                  <Divider />
                  <Statistic
                    title="客户增长"
                    value={15.2}
                    suffix="%"
                    valueStyle={{ color: '#1890ff' }}
                    prefix="↗"
                  />
                </Card>
              </Col>
              <Col span={8}>
                <Card title="本年 vs 去年" size="small">
                  <Statistic
                    title="收入增长"
                    value={25.8}
                    suffix="%"
                    valueStyle={{ color: '#52c41a' }}
                    prefix="↗"
                  />
                  <Divider />
                  <Statistic
                    title="客户增长"
                    value={22.3}
                    suffix="%"
                    valueStyle={{ color: '#1890ff' }}
                    prefix="↗"
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default BusinessReports;
