import React from 'react';
import { Card, Row, Col, Statistic, Alert, List, Tag, Button } from 'antd';
import { BookOutlined, FileTextOutlined, EyeOutlined, DownloadOutlined } from '@ant-design/icons';

const KnowledgeTest: React.FC = () => {
  const articles = [
    {
      id: 'KB-001',
      title: '核心路由器故障排查指南',
      author: '张工程师',
      createTime: '2024-01-15 10:30',
      views: 245,
      tags: ['路由器', '故障排查']
    },
    {
      id: 'KB-002',
      title: '客户专线开通标准流程',
      author: '李技术员',
      createTime: '2024-01-14 16:45',
      views: 189,
      tags: ['专线', '开通流程']
    },
    {
      id: 'KB-003',
      title: '防火墙安全策略配置手册',
      author: '王安全专员',
      createTime: '2024-01-13 09:15',
      views: 156,
      tags: ['防火墙', '安全策略']
    }
  ];

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Alert
            message="知识库管理"
            description="管理技术文档、操作手册和最佳实践"
            type="info"
            showIcon
          />
        </Col>
      </Row>

      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="文章总数"
              value={156}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="分类数量"
              value={12}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="总浏览量"
              value={8945}
              prefix={<EyeOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      <Card title="知识库文章">
        <List
          itemLayout="vertical"
          dataSource={articles}
          renderItem={(article) => (
            <List.Item
              key={article.id}
              actions={[
                <Button type="text" icon={<EyeOutlined />}>查看</Button>,
                <Button type="text" icon={<DownloadOutlined />}>下载</Button>
              ]}
            >
              <List.Item.Meta
                title={article.title}
                description={
                  <div>
                    <div>作者：{article.author} | 创建时间：{article.createTime} | 浏览：{article.views}</div>
                    <div style={{ marginTop: 8 }}>
                      {article.tags.map(tag => (
                        <Tag key={tag}>{tag}</Tag>
                      ))}
                    </div>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </Card>
    </div>
  );
};

export default KnowledgeTest;
