import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Table,
  Tag,
  Button,
  Space,
  Select,
  DatePicker,
  Input,
  Modal,
  Form,
  Statistic,
  Alert,
  Badge,
  Tooltip,
  message,
  Timeline,
  Descriptions
} from 'antd';
import {
  ExclamationCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  BellOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Search } = Input;
const { TextArea } = Input;

const AlertManagement: React.FC = () => {
  const [selectedSeverity, setSelectedSeverity] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [handleModalVisible, setHandleModalVisible] = useState(false);
  const [selectedAlert, setSelectedAlert] = useState(null);
  const [form] = Form.useForm();

  // 告警统计数据
  const alertStats = {
    total: 156,
    critical: 12,
    major: 34,
    minor: 67,
    warning: 43,
    pending: 89,
    processing: 45,
    resolved: 22
  };

  // 告警数据
  const [alertData, setAlertData] = useState([
    {
      key: '1',
      alertId: 'ALT-2024-001',
      title: '核心路由器CPU使用率过高',
      severity: 'critical',
      status: 'pending',
      source: 'RT-BJ-001',
      sourceType: '核心路由器',
      location: '北京数据中心A栋',
      description: 'CPU使用率持续超过90%，可能影响网络性能',
      occurTime: '2024-01-15 14:25:30',
      updateTime: '2024-01-15 14:25:30',
      assignee: null,
      category: '性能',
      impact: '高',
      urgency: '高',
      affectedServices: ['5G核心网', '数据中心互联'],
      rootCause: null,
      resolution: null
    },
    {
      key: '2',
      alertId: 'ALT-2024-002',
      title: '交换机端口链路中断',
      severity: 'major',
      status: 'processing',
      source: 'SW-SH-015',
      sourceType: '接入交换机',
      location: '上海数据中心B栋',
      description: '端口Gi1/0/24链路状态为Down，影响部分用户接入',
      occurTime: '2024-01-15 13:45:15',
      updateTime: '2024-01-15 14:10:22',
      assignee: '李工程师',
      category: '连接',
      impact: '中',
      urgency: '高',
      affectedServices: ['企业专线'],
      rootCause: '光纤连接器松动',
      resolution: '重新插拔光纤连接器'
    },
    {
      key: '3',
      alertId: 'ALT-2024-003',
      title: '防火墙内存使用率告警',
      severity: 'minor',
      status: 'resolved',
      source: 'FW-GZ-008',
      sourceType: '防火墙',
      location: '广州数据中心C栋',
      description: '内存使用率达到85%，接近告警阈值',
      occurTime: '2024-01-15 12:30:45',
      updateTime: '2024-01-15 13:15:30',
      assignee: '王技术员',
      category: '性能',
      impact: '低',
      urgency: '中',
      affectedServices: ['安全防护'],
      rootCause: '内存碎片化',
      resolution: '重启防火墙服务，内存使用率恢复正常'
    },
    {
      key: '4',
      alertId: 'ALT-2024-004',
      title: '负载均衡器服务异常',
      severity: 'critical',
      status: 'processing',
      source: 'LB-SZ-003',
      sourceType: '负载均衡器',
      location: '深圳数据中心D栋',
      description: '负载均衡器无响应，所有后端服务器无法访问',
      occurTime: '2024-01-15 11:20:10',
      updateTime: '2024-01-15 14:00:15',
      assignee: '张主管',
      category: '服务',
      impact: '高',
      urgency: '高',
      affectedServices: ['Web服务', 'API网关'],
      rootCause: '硬件故障',
      resolution: '正在更换硬件设备'
    },
    {
      key: '5',
      alertId: 'ALT-2024-005',
      title: '无线AP信号强度下降',
      severity: 'warning',
      status: 'pending',
      source: 'AP-HZ-056',
      sourceType: '无线AP',
      location: '杭州办公楼5层',
      description: '信号强度从-45dBm下降到-65dBm',
      occurTime: '2024-01-15 10:15:20',
      updateTime: '2024-01-15 10:15:20',
      assignee: null,
      category: '信号',
      impact: '低',
      urgency: '低',
      affectedServices: ['无线网络'],
      rootCause: null,
      resolution: null
    }
  ]);

  const getSeverityColor = (severity: string) => {
    const colors = {
      critical: 'red',
      major: 'orange',
      minor: 'yellow',
      warning: 'blue'
    };
    return colors[severity as keyof typeof colors] || 'default';
  };

  const getSeverityText = (severity: string) => {
    const texts = {
      critical: '严重',
      major: '重要',
      minor: '次要',
      warning: '警告'
    };
    return texts[severity as keyof typeof texts] || '未知';
  };

  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'orange',
      processing: 'blue',
      resolved: 'green',
      closed: 'default'
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getStatusText = (status: string) => {
    const texts = {
      pending: '待处理',
      processing: '处理中',
      resolved: '已解决',
      closed: '已关闭'
    };
    return texts[status as keyof typeof texts] || '未知';
  };

  const getSeverityIcon = (severity: string) => {
    const icons = {
      critical: <ExclamationCircleOutlined />,
      major: <WarningOutlined />,
      minor: <InfoCircleOutlined />,
      warning: <BellOutlined />
    };
    return icons[severity as keyof typeof icons] || <InfoCircleOutlined />;
  };

  const columns = [
    {
      title: '告警信息',
      key: 'alertInfo',
      width: 250,
      render: (_, record: any) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
            <Badge
              status={record.severity === 'critical' ? 'error' : 
                     record.severity === 'major' ? 'warning' : 'processing'}
              text={record.title}
            />
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.alertId} | {record.category}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            来源: {record.source}
          </div>
        </div>
      ),
    },
    {
      title: '严重程度',
      dataIndex: 'severity',
      key: 'severity',
      width: 100,
      render: (severity: string) => (
        <Tag color={getSeverityColor(severity)} icon={getSeverityIcon(severity)}>
          {getSeverityText(severity)}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '影响等级',
      key: 'impact',
      width: 100,
      render: (_, record: any) => (
        <div>
          <div>影响: <Tag size="small">{record.impact}</Tag></div>
          <div>紧急: <Tag size="small">{record.urgency}</Tag></div>
        </div>
      ),
    },
    {
      title: '位置',
      dataIndex: 'location',
      key: 'location',
      width: 150,
    },
    {
      title: '负责人',
      dataIndex: 'assignee',
      key: 'assignee',
      width: 100,
      render: (assignee: string) => assignee || <span style={{ color: '#ccc' }}>未分配</span>,
    },
    {
      title: '发生时间',
      dataIndex: 'occurTime',
      key: 'occurTime',
      width: 150,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record: any) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => showAlertDetail(record)}
            />
          </Tooltip>
          <Tooltip title="处理告警">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleAlert(record)}
            />
          </Tooltip>
          <Tooltip title="删除告警">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => deleteAlert(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const showAlertDetail = (alert: any) => {
    setSelectedAlert(alert);
    setDetailModalVisible(true);
  };

  const handleAlert = (alert: any) => {
    setSelectedAlert(alert);
    form.setFieldsValue({
      assignee: alert.assignee,
      status: alert.status,
      rootCause: alert.rootCause,
      resolution: alert.resolution
    });
    setHandleModalVisible(true);
  };

  const deleteAlert = (alert: any) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除告警 "${alert.title}" 吗？`,
      onOk() {
        setAlertData(alertData.filter(item => item.key !== alert.key));
        message.success('删除成功');
      },
    });
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      setAlertData(alertData.map(item => 
        item.key === (selectedAlert as any).key 
          ? { ...item, ...values, updateTime: new Date().toLocaleString('zh-CN') }
          : item
      ));
      message.success('更新成功');
      setHandleModalVisible(false);
    });
  };

  const filteredData = alertData.filter(alert => {
    const severityMatch = selectedSeverity === 'all' || alert.severity === selectedSeverity;
    const statusMatch = selectedStatus === 'all' || alert.status === selectedStatus;
    return severityMatch && statusMatch;
  });

  return (
    <div>
      {/* 告警统计 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="告警总数"
              value={alertStats.total}
              prefix={<BellOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="严重告警"
              value={alertStats.critical}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="待处理"
              value={alertStats.pending}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="已解决"
              value={alertStats.resolved}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 告警列表 */}
      <Card
        title="告警管理"
        extra={
          <Space>
            <Search
              placeholder="搜索告警"
              allowClear
              style={{ width: 200 }}
            />
            <Select
              value={selectedSeverity}
              onChange={setSelectedSeverity}
              style={{ width: 120 }}
            >
              <Option value="all">全部级别</Option>
              <Option value="critical">严重</Option>
              <Option value="major">重要</Option>
              <Option value="minor">次要</Option>
              <Option value="warning">警告</Option>
            </Select>
            <Select
              value={selectedStatus}
              onChange={setSelectedStatus}
              style={{ width: 120 }}
            >
              <Option value="all">全部状态</Option>
              <Option value="pending">待处理</Option>
              <Option value="processing">处理中</Option>
              <Option value="resolved">已解决</Option>
              <Option value="closed">已关闭</Option>
            </Select>
            <RangePicker size="small" />
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={filteredData}
          pagination={{
            total: filteredData.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 告警详情模态框 */}
      <Modal
        title="告警详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedAlert && (
          <div>
            <Descriptions column={2} bordered>
              <Descriptions.Item label="告警ID">{(selectedAlert as any).alertId}</Descriptions.Item>
              <Descriptions.Item label="告警标题">{(selectedAlert as any).title}</Descriptions.Item>
              <Descriptions.Item label="严重程度">
                <Tag color={getSeverityColor((selectedAlert as any).severity)}>
                  {getSeverityText((selectedAlert as any).severity)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={getStatusColor((selectedAlert as any).status)}>
                  {getStatusText((selectedAlert as any).status)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="告警源">{(selectedAlert as any).source}</Descriptions.Item>
              <Descriptions.Item label="设备类型">{(selectedAlert as any).sourceType}</Descriptions.Item>
              <Descriptions.Item label="位置">{(selectedAlert as any).location}</Descriptions.Item>
              <Descriptions.Item label="分类">{(selectedAlert as any).category}</Descriptions.Item>
              <Descriptions.Item label="影响等级">{(selectedAlert as any).impact}</Descriptions.Item>
              <Descriptions.Item label="紧急程度">{(selectedAlert as any).urgency}</Descriptions.Item>
              <Descriptions.Item label="负责人">{(selectedAlert as any).assignee || '未分配'}</Descriptions.Item>
              <Descriptions.Item label="发生时间">{(selectedAlert as any).occurTime}</Descriptions.Item>
              <Descriptions.Item label="更新时间">{(selectedAlert as any).updateTime}</Descriptions.Item>
              <Descriptions.Item label="受影响服务" span={2}>
                {(selectedAlert as any).affectedServices.map((service: string, index: number) => (
                  <Tag key={index}>{service}</Tag>
                ))}
              </Descriptions.Item>
              <Descriptions.Item label="告警描述" span={2}>
                {(selectedAlert as any).description}
              </Descriptions.Item>
              {(selectedAlert as any).rootCause && (
                <Descriptions.Item label="根本原因" span={2}>
                  {(selectedAlert as any).rootCause}
                </Descriptions.Item>
              )}
              {(selectedAlert as any).resolution && (
                <Descriptions.Item label="解决方案" span={2}>
                  {(selectedAlert as any).resolution}
                </Descriptions.Item>
              )}
            </Descriptions>
          </div>
        )}
      </Modal>

      {/* 处理告警模态框 */}
      <Modal
        title="处理告警"
        open={handleModalVisible}
        onOk={handleModalOk}
        onCancel={() => setHandleModalVisible(false)}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="assignee"
            label="分配给"
          >
            <Select placeholder="选择负责人">
              <Option value="张主管">张主管</Option>
              <Option value="李工程师">李工程师</Option>
              <Option value="王技术员">王技术员</Option>
              <Option value="刘运维">刘运维</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select placeholder="选择状态">
              <Option value="pending">待处理</Option>
              <Option value="processing">处理中</Option>
              <Option value="resolved">已解决</Option>
              <Option value="closed">已关闭</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="rootCause"
            label="根本原因"
          >
            <TextArea
              rows={3}
              placeholder="请输入根本原因分析"
            />
          </Form.Item>

          <Form.Item
            name="resolution"
            label="解决方案"
          >
            <TextArea
              rows={4}
              placeholder="请输入解决方案或处理步骤"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AlertManagement;
