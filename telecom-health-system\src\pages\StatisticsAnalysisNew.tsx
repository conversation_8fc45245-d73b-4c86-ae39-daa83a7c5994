import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Select,
  Button,
  Space,
  Alert,
  Progress,
  Tag,
  Tabs,
  Table,
  Typography
} from 'antd';
import {
  BarChartOutlined,
  PieChartOutlined,
  LineChartOutlined,
  DotChartOutlined,
  FundOutlined,
  TrendingUpOutlined,
  CalculatorOutlined,
  FileExcelOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { TabPane } = Tabs;
const { Title } = Typography;

const StatisticsAnalysisNew: React.FC = () => {
  const [selectedDimension, setSelectedDimension] = useState('region');
  const [selectedMetric, setSelectedMetric] = useState('revenue');

  const statsOverview = {
    totalDataPoints: 15680,
    analysisModels: 12,
    correlationCoeff: 0.85,
    predictionAccuracy: 89.5,
    dataQuality: 96.2,
    updateFrequency: '实时'
  };

  const dimensionStats = [
    {
      key: '1',
      dimension: '北京',
      revenue: 8500000,
      customers: 856,
      satisfaction: 4.3,
      growth: 12.5,
      marketShare: 28.5,
      incidents: 15
    },
    {
      key: '2',
      dimension: '上海',
      revenue: 6200000,
      customers: 634,
      satisfaction: 4.1,
      growth: 8.9,
      marketShare: 22.1,
      incidents: 23
    },
    {
      key: '3',
      dimension: '广州',
      revenue: 5800000,
      customers: 567,
      satisfaction: 4.0,
      growth: 15.2,
      marketShare: 19.8,
      incidents: 18
    }
  ];

  const columns = [
    {
      title: '维度',
      dataIndex: 'dimension',
      key: 'dimension',
      width: 100,
    },
    {
      title: '收入(万元)',
      dataIndex: 'revenue',
      key: 'revenue',
      width: 120,
      render: (value: number) => (value / 10000).toFixed(0),
    },
    {
      title: '客户数',
      dataIndex: 'customers',
      key: 'customers',
      width: 100,
    },
    {
      title: '满意度',
      dataIndex: 'satisfaction',
      key: 'satisfaction',
      width: 120,
      render: (score: number) => (
        <Progress
          percent={score * 20}
          size="small"
          format={() => `${score}/5.0`}
          strokeColor="#faad14"
        />
      ),
    },
    {
      title: '增长率(%)',
      dataIndex: 'growth',
      key: 'growth',
      width: 100,
      render: (growth: number) => (
        <span style={{ color: growth > 10 ? '#52c41a' : growth > 5 ? '#1890ff' : '#faad14' }}>
          {growth.toFixed(1)}%
        </span>
      ),
    },
    {
      title: '市场份额(%)',
      dataIndex: 'marketShare',
      key: 'marketShare',
      width: 120,
      render: (share: number) => (
        <Progress
          percent={share}
          size="small"
          format={(percent) => `${percent}%`}
        />
      ),
    },
    {
      title: '故障数',
      dataIndex: 'incidents',
      key: 'incidents',
      width: 80,
      render: (count: number) => (
        <Tag color={count > 25 ? 'red' : count > 15 ? 'orange' : 'green'}>
          {count}
        </Tag>
      ),
    }
  ];

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Alert
            message="统计分析中心"
            description="提供多维度数据统计分析，支持自定义指标和可视化展示"
            type="info"
            showIcon
          />
        </Col>
      </Row>

      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={8} lg={4}>
          <Card>
            <Statistic
              title="数据点总数"
              value={statsOverview.totalDataPoints}
              prefix={<DotChartOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8} lg={4}>
          <Card>
            <Statistic
              title="分析模型"
              value={statsOverview.analysisModels}
              prefix={<CalculatorOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8} lg={4}>
          <Card>
            <Statistic
              title="相关系数"
              value={statsOverview.correlationCoeff}
              precision={2}
              prefix={<FundOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8} lg={4}>
          <Card>
            <Statistic
              title="预测准确率"
              value={statsOverview.predictionAccuracy}
              suffix="%"
              precision={1}
              prefix={<TrendingUpOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8} lg={4}>
          <Card>
            <Statistic
              title="数据质量"
              value={statsOverview.dataQuality}
              suffix="%"
              precision={1}
              prefix={<BarChartOutlined />}
              valueStyle={{ color: '#13c2c2' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8} lg={4}>
          <Card>
            <Statistic
              title="更新频率"
              value={statsOverview.updateFrequency}
              prefix={<LineChartOutlined />}
              valueStyle={{ color: '#eb2f96' }}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <Tabs 
          defaultActiveKey="multidim"
          tabBarExtraContent={
            <Space>
              <Select
                value={selectedDimension}
                onChange={setSelectedDimension}
                style={{ width: 120 }}
              >
                <Option value="region">按区域</Option>
                <Option value="service">按服务</Option>
                <Option value="customer">按客户</Option>
                <Option value="time">按时间</Option>
              </Select>
              <Select
                value={selectedMetric}
                onChange={setSelectedMetric}
                style={{ width: 120 }}
              >
                <Option value="revenue">收入</Option>
                <Option value="customer">客户</Option>
                <Option value="quality">质量</Option>
                <Option value="performance">性能</Option>
              </Select>
              <Button icon={<FileExcelOutlined />} size="small">
                导出数据
              </Button>
            </Space>
          }
        >
          <TabPane tab="多维度分析" key="multidim">
            <div style={{ marginBottom: 16 }}>
              <Title level={4}>多维度统计分析</Title>
            </div>
            <Table
              columns={columns}
              dataSource={dimensionStats}
              pagination={false}
              scroll={{ x: 800 }}
            />
          </TabPane>

          <TabPane tab="相关性分析" key="correlation">
            <Row gutter={16}>
              <Col span={12}>
                <Card title="指标相关性矩阵" size="small">
                  <div style={{ height: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#999' }}>
                    相关性热力图 (可集成 ECharts)
                  </div>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="散点分析" size="small">
                  <div style={{ height: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#999' }}>
                    散点图分析 (可集成 ECharts)
                  </div>
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="分布分析" key="distribution">
            <Row gutter={16}>
              <Col span={8}>
                <Card title="收入分布" size="small">
                  <div style={{ height: '200px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#999' }}>
                    直方图 (可集成 ECharts)
                  </div>
                </Card>
              </Col>
              <Col span={8}>
                <Card title="客户分布" size="small">
                  <div style={{ height: '200px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#999' }}>
                    饼图 (可集成 ECharts)
                  </div>
                </Card>
              </Col>
              <Col span={8}>
                <Card title="满意度分布" size="small">
                  <div style={{ height: '200px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#999' }}>
                    箱线图 (可集成 ECharts)
                  </div>
                </Card>
              </Col>
            </Row>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default StatisticsAnalysisNew;
