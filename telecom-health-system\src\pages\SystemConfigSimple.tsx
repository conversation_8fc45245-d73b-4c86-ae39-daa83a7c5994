import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Form,
  Input,
  InputNumber,
  Switch,
  Button,
  Select,
  Alert,
  Tabs,
  Divider,
  message,
  Space,
  Typography
} from 'antd';
import {
  SettingOutlined,
  SaveOutlined,
  ReloadOutlined,
  SecurityScanOutlined,
  MailOutlined,
  DatabaseOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { TabPane } = Tabs;
const { Title, Text } = Typography;
const { TextArea } = Input;

const SystemConfigSimple: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSave = () => {
    setLoading(true);
    form.validateFields().then(() => {
      setTimeout(() => {
        setLoading(false);
        message.success('配置保存成功');
      }, 1000);
    }).catch(() => {
      setLoading(false);
    });
  };

  const handleReset = () => {
    form.resetFields();
    message.info('配置已重置');
  };

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Alert
            message="系统配置"
            description="配置系统运行参数、安全策略和通知设置"
            type="info"
            showIcon
          />
        </Col>
      </Row>

      <Card>
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            systemName: '电信网络客户健康档案系统',
            sessionTimeout: 30,
            autoRefresh: true,
            refreshInterval: 30,
            enableNotification: true,
            enableEmail: true,
            enableSMS: false,
            maxLoginAttempts: 5,
            passwordMinLength: 6,
            enableTwoFactor: false,
            dataRetentionDays: 365,
            backupEnabled: true,
            backupInterval: 24
          }}
        >
          <Tabs defaultActiveKey="general">
            <TabPane tab="基本设置" key="general" icon={<SettingOutlined />}>
              <Title level={4}>系统基本配置</Title>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="systemName"
                    label="系统名称"
                    rules={[{ required: true, message: '请输入系统名称' }]}
                  >
                    <Input placeholder="请输入系统名称" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="systemVersion"
                    label="系统版本"
                  >
                    <Input placeholder="v1.0.0" disabled />
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="sessionTimeout"
                    label="会话超时时间（分钟）"
                    rules={[{ required: true, message: '请输入会话超时时间' }]}
                  >
                    <InputNumber min={5} max={480} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="refreshInterval"
                    label="数据刷新间隔（秒）"
                    rules={[{ required: true, message: '请输入刷新间隔' }]}
                  >
                    <InputNumber min={10} max={300} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="autoRefresh"
                    label="自动刷新"
                    valuePropName="checked"
                  >
                    <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="enableNotification"
                    label="系统通知"
                    valuePropName="checked"
                  >
                    <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="systemDescription"
                label="系统描述"
              >
                <TextArea rows={3} placeholder="请输入系统描述" />
              </Form.Item>
            </TabPane>

            <TabPane tab="安全设置" key="security" icon={<SecurityScanOutlined />}>
              <Title level={4}>安全策略配置</Title>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="maxLoginAttempts"
                    label="最大登录尝试次数"
                    rules={[{ required: true, message: '请输入最大登录尝试次数' }]}
                  >
                    <InputNumber min={3} max={10} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="passwordMinLength"
                    label="密码最小长度"
                    rules={[{ required: true, message: '请输入密码最小长度' }]}
                  >
                    <InputNumber min={6} max={20} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="enableTwoFactor"
                    label="双因子认证"
                    valuePropName="checked"
                  >
                    <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="enableIPWhitelist"
                    label="IP白名单"
                    valuePropName="checked"
                  >
                    <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="allowedIPs"
                label="允许的IP地址"
                help="每行一个IP地址或IP段，例如：***********/24"
              >
                <TextArea rows={4} placeholder="请输入允许的IP地址" />
              </Form.Item>
            </TabPane>

            <TabPane tab="通知设置" key="notification" icon={<MailOutlined />}>
              <Title level={4}>通知配置</Title>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="enableEmail"
                    label="邮件通知"
                    valuePropName="checked"
                  >
                    <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="enableSMS"
                    label="短信通知"
                    valuePropName="checked"
                  >
                    <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="smtpServer"
                    label="SMTP服务器"
                  >
                    <Input placeholder="smtp.example.com" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="smtpPort"
                    label="SMTP端口"
                  >
                    <InputNumber min={1} max={65535} style={{ width: '100%' }} placeholder="587" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="smtpUsername"
                    label="SMTP用户名"
                  >
                    <Input placeholder="请输入SMTP用户名" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="smtpPassword"
                    label="SMTP密码"
                  >
                    <Input.Password placeholder="请输入SMTP密码" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="defaultRecipients"
                label="默认收件人"
                help="多个邮箱地址用逗号分隔"
              >
                <Input placeholder="<EMAIL>, <EMAIL>" />
              </Form.Item>
            </TabPane>

            <TabPane tab="数据设置" key="data" icon={<DatabaseOutlined />}>
              <Title level={4}>数据管理配置</Title>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="dataRetentionDays"
                    label="数据保留天数"
                    rules={[{ required: true, message: '请输入数据保留天数' }]}
                  >
                    <InputNumber min={30} max={3650} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="maxRecordsPerQuery"
                    label="单次查询最大记录数"
                  >
                    <InputNumber min={100} max={10000} style={{ width: '100%' }} placeholder="1000" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="backupEnabled"
                    label="自动备份"
                    valuePropName="checked"
                  >
                    <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="backupInterval"
                    label="备份间隔（小时）"
                  >
                    <InputNumber min={1} max={168} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="backupPath"
                label="备份路径"
              >
                <Input placeholder="/backup/telecom-health-system" />
              </Form.Item>
            </TabPane>
          </Tabs>

          <Divider />
          
          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button icon={<ReloadOutlined />} onClick={handleReset}>
                重置
              </Button>
              <Button 
                type="primary" 
                icon={<SaveOutlined />} 
                loading={loading}
                onClick={handleSave}
              >
                保存配置
              </Button>
            </Space>
          </div>
        </Form>
      </Card>
    </div>
  );
};

export default SystemConfigSimple;
