import React, { useState } from 'react';
import {
  Layout,
  Menu,
  Avatar,
  Dropdown,
  Space,
  Badge,
  Button,
  Typography,
  Breadcrumb
} from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  BellOutlined,
  LogoutOutlined,
  SettingOutlined,
  WifiOutlined,
  DashboardOutlined,
  TeamOutlined,
  MonitorOutlined,
  HeartOutlined,
  BarChartOutlined,
  DatabaseOutlined,
  SafetyOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate, useLocation, Outlet } from 'react-router-dom';
import type { MenuProps } from 'antd';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

const MainLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人信息',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  const menuItems: MenuProps['items'] = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表盘',
    },
    {
      key: 'customer',
      icon: <TeamOutlined />,
      label: '客户管理',
      children: [
        {
          key: '/customer/list',
          label: '客户信息',
        },
        {
          key: '/customer/profile',
          label: '客户档案',
        },
        {
          key: '/customer/category',
          label: '客户分类',
        },
      ],
    },
    {
      key: 'network',
      icon: <MonitorOutlined />,
      label: '网络监控',
      children: [
        {
          key: '/network/status',
          label: '网络状态',
        },
        {
          key: '/network/devices',
          label: '设备监控',
        },
        {
          key: '/network/alerts',
          label: '故障告警',
        },
        {
          key: '/network/topology',
          label: '网络拓扑',
        },
      ],
    },
    {
      key: 'health',
      icon: <HeartOutlined />,
      label: '健康评估',
      children: [
        {
          key: '/health/overview',
          label: '网络健康度',
        },
        {
          key: '/health/performance',
          label: '性能指标',
        },
        {
          key: '/health/quality',
          label: '质量分析',
        },
        {
          key: '/health/prediction',
          label: '预测分析',
        },
      ],
    },
    {
      key: 'reports',
      icon: <BarChartOutlined />,
      label: '报表统计',
      children: [
        {
          key: '/reports/business',
          label: '业务报表',
        },
        {
          key: '/reports/statistics',
          label: '统计分析',
        },
        {
          key: '/reports/export',
          label: '数据导出',
        },
      ],
    },
    {
      key: 'maintenance',
      icon: <SafetyOutlined />,
      label: '运维管理',
      children: [
        {
          key: '/maintenance/schedule',
          label: '维护计划',
        },
        {
          key: '/maintenance/tickets',
          label: '工单管理',
        },
        {
          key: '/maintenance/knowledge',
          label: '知识库',
        },
      ],
    },
    {
      key: 'system',
      icon: <DatabaseOutlined />,
      label: '系统管理',
      children: [
        {
          key: '/system/users',
          label: '用户管理',
        },
        {
          key: '/system/roles',
          label: '权限设置',
        },
        {
          key: '/system/config',
          label: '系统配置',
        },
        {
          key: '/system/logs',
          label: '操作日志',
        },
      ],
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    if (key.startsWith('/')) {
      navigate(key);
    }
  };

  const getSelectedKeys = () => {
    return [location.pathname];
  };

  const getOpenKeys = () => {
    const pathSegments = location.pathname.split('/');
    if (pathSegments.length > 2) {
      return [pathSegments[1]];
    }
    return [];
  };

  const getBreadcrumbItems = () => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbItems = [
      {
        title: '首页',
        href: '/dashboard',
      },
    ];

    if (pathSegments.length > 1) {
      const menuMap: Record<string, string> = {
        customer: '客户管理',
        network: '网络监控',
        health: '健康评估',
        reports: '报表统计',
        maintenance: '运维管理',
        system: '系统管理',
      };

      const subMenuMap: Record<string, string> = {
        list: '客户信息',
        profile: '客户档案',
        category: '客户分类',
        status: '网络状态',
        devices: '设备监控',
        alerts: '故障告警',
        topology: '网络拓扑',
        overview: '网络健康度',
        performance: '性能指标',
        quality: '质量分析',
        prediction: '预测分析',
        business: '业务报表',
        statistics: '统计分析',
        export: '数据导出',
        schedule: '维护计划',
        tickets: '工单管理',
        knowledge: '知识库',
        users: '用户管理',
        roles: '权限设置',
        config: '系统配置',
        logs: '操作日志',
      };

      if (menuMap[pathSegments[0]]) {
        breadcrumbItems.push({
          title: menuMap[pathSegments[0]],
        });
      }

      if (pathSegments[1] && subMenuMap[pathSegments[1]]) {
        breadcrumbItems.push({
          title: subMenuMap[pathSegments[1]],
        });
      }
    }

    return breadcrumbItems;
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider trigger={null} collapsible collapsed={collapsed} theme="dark">
        <div className="logo" style={{ 
          height: 64, 
          margin: 16, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: collapsed ? 'center' : 'flex-start',
          color: 'white',
          fontSize: collapsed ? 24 : 16,
          fontWeight: 'bold'
        }}>
          <WifiOutlined style={{ marginRight: collapsed ? 0 : 8 }} />
          {!collapsed && '电信健康档案'}
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={getSelectedKeys()}
          defaultOpenKeys={getOpenKeys()}
          items={menuItems}
          onClick={handleMenuClick}
        />
      </Sider>
      <Layout>
        <Header style={{ 
          padding: '0 16px', 
          background: '#fff', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          boxShadow: '0 1px 4px rgba(0,21,41,.08)'
        }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{
                fontSize: '16px',
                width: 64,
                height: 64,
              }}
            />
          </div>
          
          <Space size="middle">
            <Badge count={5}>
              <BellOutlined style={{ fontSize: 18, cursor: 'pointer' }} />
            </Badge>
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <Space style={{ cursor: 'pointer' }}>
                <Avatar icon={<UserOutlined />} />
                <div>
                  <Text strong>{user?.username}</Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    {user?.role}
                  </Text>
                </div>
              </Space>
            </Dropdown>
          </Space>
        </Header>
        <Content style={{ margin: '16px' }}>
          <Breadcrumb 
            items={getBreadcrumbItems()} 
            style={{ marginBottom: 16 }}
          />
          <div style={{ 
            padding: 24, 
            minHeight: 360, 
            background: '#fff',
            borderRadius: 8,
            boxShadow: '0 1px 2px rgba(0,0,0,0.03), 0 1px 6px -1px rgba(0,0,0,0.02), 0 2px 4px rgba(0,0,0,0.02)'
          }}>
            <Outlet />
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
