import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Select,
  DatePicker,
  Button,
  Space,
  Alert,
  Progress,
  Tag,
  Tooltip,
  Tabs,
  Rate,
  Timeline,
  Descriptions
} from 'antd';
import {
  StarOutlined,
  TrophyOutlined,
  UserOutlined,
  PhoneOutlined,
  WifiOutlined,
  CheckCircleOutlined,
  Exclamation<PERSON>ircleOutlined,
  ClockCircleOutlined,
  Line<PERSON>hartOutlined,
  Bar<PERSON>hartOutlined,
  PieChartOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

const QualityAnalysis: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('7d');
  const [selectedService, setSelectedService] = useState('all');

  // 服务质量概览
  const qualityOverview = {
    overallScore: 4.2,
    customerSatisfaction: 92.5,
    serviceAvailability: 99.95,
    responseTime: 8.5,
    resolutionRate: 95.8,
    escalationRate: 2.3
  };

  // 服务质量指标
  const qualityMetrics = [
    {
      key: '1',
      serviceName: '5G核心网服务',
      serviceType: '核心网',
      customerCount: 156,
      satisfactionScore: 4.5,
      availability: 99.98,
      avgResponseTime: 6.2,
      issueCount: 3,
      resolutionRate: 98.5,
      slaCompliance: 99.2,
      status: 'excellent'
    },
    {
      key: '2',
      serviceName: '数据中心互联',
      serviceType: '专线',
      customerCount: 89,
      satisfactionScore: 4.3,
      availability: 99.95,
      avgResponseTime: 8.1,
      issueCount: 5,
      resolutionRate: 96.8,
      slaCompliance: 98.5,
      status: 'good'
    },
    {
      key: '3',
      serviceName: '云网融合服务',
      serviceType: '云服务',
      customerCount: 234,
      satisfactionScore: 4.1,
      availability: 99.85,
      avgResponseTime: 12.5,
      issueCount: 12,
      resolutionRate: 94.2,
      slaCompliance: 96.8,
      status: 'good'
    },
    {
      key: '4',
      serviceName: '物联网平台',
      serviceType: 'IoT',
      customerCount: 67,
      satisfactionScore: 3.8,
      availability: 99.12,
      avgResponseTime: 18.3,
      issueCount: 18,
      resolutionRate: 89.5,
      slaCompliance: 92.1,
      status: 'warning'
    },
    {
      key: '5',
      serviceName: '企业专线',
      serviceType: '专线',
      customerCount: 445,
      satisfactionScore: 4.0,
      availability: 99.78,
      avgResponseTime: 15.2,
      issueCount: 25,
      resolutionRate: 92.8,
      slaCompliance: 95.5,
      status: 'good'
    }
  ];

  // 客户反馈数据
  const customerFeedback = [
    {
      key: '1',
      customerName: '中国移动北京分公司',
      serviceName: '5G核心网服务',
      rating: 5,
      feedback: '服务稳定，响应及时，技术支持专业',
      feedbackTime: '2024-01-15 14:25',
      category: '服务质量',
      status: 'positive'
    },
    {
      key: '2',
      customerName: '联通上海数据中心',
      serviceName: '数据中心互联',
      rating: 4,
      feedback: '整体满意，但希望能进一步提升故障响应速度',
      feedbackTime: '2024-01-15 13:45',
      category: '响应时间',
      status: 'neutral'
    },
    {
      key: '3',
      customerName: '电信广州核心网',
      serviceName: '云网融合服务',
      rating: 3,
      feedback: '服务基本满足需求，但偶有延迟问题',
      feedbackTime: '2024-01-15 12:30',
      category: '性能',
      status: 'negative'
    },
    {
      key: '4',
      customerName: '移动深圳基站群',
      serviceName: '物联网平台',
      rating: 2,
      feedback: '近期故障较多，影响业务正常运行',
      feedbackTime: '2024-01-15 11:15',
      category: '可靠性',
      status: 'negative'
    }
  ];

  // 质量改进计划
  const improvementPlans = [
    {
      time: '2024-01-20',
      title: '物联网平台性能优化',
      description: '针对客户反馈的延迟问题，计划升级硬件设备',
      priority: 'high',
      status: 'planned'
    },
    {
      time: '2024-01-18',
      title: '故障响应流程优化',
      description: '缩短故障响应时间，提升客户满意度',
      priority: 'medium',
      status: 'in-progress'
    },
    {
      time: '2024-01-15',
      title: '5G核心网扩容完成',
      description: '成功完成核心网扩容，提升服务质量',
      priority: 'high',
      status: 'completed'
    },
    {
      time: '2024-01-12',
      title: '客户服务培训',
      description: '完成技术支持团队专业技能培训',
      priority: 'medium',
      status: 'completed'
    }
  ];

  const getQualityStatus = (score: number) => {
    if (score >= 4.5) return { text: '优秀', color: 'green' };
    if (score >= 4.0) return { text: '良好', color: 'blue' };
    if (score >= 3.5) return { text: '一般', color: 'orange' };
    return { text: '较差', color: 'red' };
  };

  const getFeedbackStatus = (status: string) => {
    const statusMap = {
      positive: { text: '正面', color: 'green' },
      neutral: { text: '中性', color: 'blue' },
      negative: { text: '负面', color: 'red' }
    };
    return statusMap[status as keyof typeof statusMap] || { text: '未知', color: 'default' };
  };

  const qualityColumns = [
    {
      title: '服务名称',
      key: 'serviceInfo',
      width: 200,
      render: (_, record: any) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.serviceName}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.serviceType} | {record.customerCount}个客户
          </div>
        </div>
      ),
    },
    {
      title: '满意度评分',
      dataIndex: 'satisfactionScore',
      key: 'satisfactionScore',
      width: 120,
      render: (score: number) => (
        <div>
          <Rate disabled value={score} allowHalf />
          <div style={{ fontSize: '12px', color: '#666' }}>{score}/5.0</div>
        </div>
      ),
    },
    {
      title: '可用性',
      dataIndex: 'availability',
      key: 'availability',
      width: 100,
      render: (availability: number) => (
        <div>
          <div>{availability}%</div>
          <Progress
            percent={availability}
            size="small"
            strokeColor={availability >= 99.9 ? '#52c41a' : availability >= 99.5 ? '#1890ff' : '#faad14'}
            showInfo={false}
          />
        </div>
      ),
    },
    {
      title: '响应时间',
      dataIndex: 'avgResponseTime',
      key: 'avgResponseTime',
      width: 100,
      render: (time: number) => `${time}分钟`,
    },
    {
      title: '问题数量',
      dataIndex: 'issueCount',
      key: 'issueCount',
      width: 80,
      render: (count: number) => (
        <span style={{ color: count > 15 ? '#ff4d4f' : count > 10 ? '#faad14' : '#52c41a' }}>
          {count}
        </span>
      ),
    },
    {
      title: '解决率',
      dataIndex: 'resolutionRate',
      key: 'resolutionRate',
      width: 100,
      render: (rate: number) => (
        <Progress
          percent={rate}
          size="small"
          strokeColor={rate >= 95 ? '#52c41a' : rate >= 90 ? '#1890ff' : '#faad14'}
          format={(percent) => `${percent}%`}
        />
      ),
    },
    {
      title: 'SLA达成率',
      dataIndex: 'slaCompliance',
      key: 'slaCompliance',
      width: 120,
      render: (compliance: number) => (
        <Progress
          percent={compliance}
          size="small"
          strokeColor={compliance >= 98 ? '#52c41a' : compliance >= 95 ? '#1890ff' : '#faad14'}
          format={(percent) => `${percent}%`}
        />
      ),
    },
    {
      title: '质量等级',
      dataIndex: 'satisfactionScore',
      key: 'status',
      width: 100,
      render: (score: number) => {
        const status = getQualityStatus(score);
        return <Tag color={status.color}>{status.text}</Tag>;
      },
    }
  ];

  const feedbackColumns = [
    {
      title: '客户信息',
      key: 'customerInfo',
      width: 200,
      render: (_, record: any) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.customerName}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.serviceName}</div>
        </div>
      ),
    },
    {
      title: '评分',
      dataIndex: 'rating',
      key: 'rating',
      width: 120,
      render: (rating: number) => (
        <Rate disabled value={rating} />
      ),
    },
    {
      title: '反馈内容',
      dataIndex: 'feedback',
      key: 'feedback',
      ellipsis: true,
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100,
    },
    {
      title: '情感倾向',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const statusInfo = getFeedbackStatus(status);
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
      },
    },
    {
      title: '反馈时间',
      dataIndex: 'feedbackTime',
      key: 'feedbackTime',
      width: 150,
    }
  ];

  return (
    <div>
      {/* 概览信息 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Alert
            message="服务质量分析"
            description="基于客户反馈、SLA达成率、服务可用性等多维度分析服务质量"
            type="info"
            showIcon
          />
        </Col>
      </Row>

      {/* 质量概览 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={8} lg={4}>
          <Card>
            <Statistic
              title="整体评分"
              value={qualityOverview.overallScore}
              suffix="/5.0"
              precision={1}
              prefix={<StarOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
            <div style={{ marginTop: 8 }}>
              <Rate disabled value={qualityOverview.overallScore} allowHalf />
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={8} lg={4}>
          <Card>
            <Statistic
              title="客户满意度"
              value={qualityOverview.customerSatisfaction}
              suffix="%"
              precision={1}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8} lg={4}>
          <Card>
            <Statistic
              title="服务可用性"
              value={qualityOverview.serviceAvailability}
              suffix="%"
              precision={2}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8} lg={4}>
          <Card>
            <Statistic
              title="平均响应时间"
              value={qualityOverview.responseTime}
              suffix="分钟"
              precision={1}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8} lg={4}>
          <Card>
            <Statistic
              title="问题解决率"
              value={qualityOverview.resolutionRate}
              suffix="%"
              precision={1}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8} lg={4}>
          <Card>
            <Statistic
              title="升级率"
              value={qualityOverview.escalationRate}
              suffix="%"
              precision={1}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 详细分析 */}
      <Card>
        <Tabs defaultActiveKey="services">
          <TabPane tab="服务质量" key="services">
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Select
                  value={selectedService}
                  onChange={setSelectedService}
                  style={{ width: 150 }}
                >
                  <Option value="all">全部服务</Option>
                  <Option value="core">核心网</Option>
                  <Option value="cloud">云服务</Option>
                  <Option value="iot">物联网</Option>
                  <Option value="line">专线</Option>
                </Select>
                <Select
                  value={selectedPeriod}
                  onChange={setSelectedPeriod}
                  style={{ width: 120 }}
                >
                  <Option value="1d">最近1天</Option>
                  <Option value="7d">最近7天</Option>
                  <Option value="30d">最近30天</Option>
                  <Option value="90d">最近90天</Option>
                </Select>
                <RangePicker size="small" />
              </Space>
            </div>
            <Table
              columns={qualityColumns}
              dataSource={qualityMetrics}
              pagination={false}
              scroll={{ x: 1000 }}
            />
          </TabPane>

          <TabPane tab="客户反馈" key="feedback">
            <Table
              columns={feedbackColumns}
              dataSource={customerFeedback}
              pagination={{
                total: customerFeedback.length,
                pageSize: 10,
                showSizeChanger: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
              }}
              scroll={{ x: 800 }}
            />
          </TabPane>

          <TabPane tab="质量趋势" key="trend">
            <Row gutter={16}>
              <Col span={12}>
                <Card title="满意度趋势" size="small">
                  <div style={{ height: '200px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#999' }}>
                    满意度趋势图表 (可集成 ECharts)
                  </div>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="SLA达成率趋势" size="small">
                  <div style={{ height: '200px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#999' }}>
                    SLA达成率趋势图表 (可集成 ECharts)
                  </div>
                </Card>
              </Col>
            </Row>
            <Row gutter={16} style={{ marginTop: 16 }}>
              <Col span={12}>
                <Card title="问题分布" size="small">
                  <div style={{ height: '200px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#999' }}>
                    问题分布饼图 (可集成 ECharts)
                  </div>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="响应时间分析" size="small">
                  <div style={{ height: '200px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#999' }}>
                    响应时间柱状图 (可集成 ECharts)
                  </div>
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="改进计划" key="improvement">
            <Timeline>
              {improvementPlans.map((plan, index) => (
                <Timeline.Item
                  key={index}
                  color={
                    plan.status === 'completed' ? 'green' :
                    plan.status === 'in-progress' ? 'blue' : 'orange'
                  }
                  dot={
                    plan.status === 'completed' ? <CheckCircleOutlined /> :
                    plan.status === 'in-progress' ? <ClockCircleOutlined /> :
                    <ExclamationCircleOutlined />
                  }
                >
                  <div>
                    <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
                      {plan.title}
                      <Tag 
                        style={{ marginLeft: 8 }}
                        color={
                          plan.priority === 'high' ? 'red' :
                          plan.priority === 'medium' ? 'orange' : 'blue'
                        }
                      >
                        {plan.priority === 'high' ? '高优先级' :
                         plan.priority === 'medium' ? '中优先级' : '低优先级'}
                      </Tag>
                      <Tag 
                        style={{ marginLeft: 4 }}
                        color={
                          plan.status === 'completed' ? 'green' :
                          plan.status === 'in-progress' ? 'blue' : 'orange'
                        }
                      >
                        {plan.status === 'completed' ? '已完成' :
                         plan.status === 'in-progress' ? '进行中' : '计划中'}
                      </Tag>
                    </div>
                    <div style={{ color: '#666', marginBottom: 4 }}>{plan.description}</div>
                    <div style={{ fontSize: '12px', color: '#999' }}>{plan.time}</div>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default QualityAnalysis;
