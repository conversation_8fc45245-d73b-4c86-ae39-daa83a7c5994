.login-container {
  position: relative;
  min-height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
}

.network-animation {
  position: absolute;
  width: 100%;
  height: 100%;
}

.network-node {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.network-node:nth-child(1) {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.network-node:nth-child(2) {
  top: 60%;
  left: 20%;
  animation-delay: 1s;
}

.network-node:nth-child(3) {
  top: 30%;
  left: 80%;
  animation-delay: 2s;
}

.network-node:nth-child(4) {
  top: 80%;
  left: 70%;
  animation-delay: 3s;
}

.network-node:nth-child(5) {
  top: 50%;
  left: 50%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20px) scale(1.2);
    opacity: 1;
  }
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 32px;
  width: 100%;
  max-width: 400px;
  margin: 20px;
  position: relative;
  z-index: 10;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-icon {
  font-size: 48px;
  color: #1890ff;
  margin-bottom: 16px;
  display: block;
}

.login-header h1 {
  color: #262626;
  margin-bottom: 8px;
  font-size: 24px;
  font-weight: 600;
}

.login-header p {
  color: #8c8c8c;
  margin-bottom: 0;
  font-size: 14px;
}

.login-button {
  height: 44px;
  border-radius: 8px;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  font-weight: 500;
}

.login-button:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
}

.login-form-forgot {
  color: #1890ff;
}

.login-demo {
  margin-top: 20px;
  padding: 16px;
  background: #f6f8fa;
  border-radius: 8px;
  text-align: center;
}

.login-demo p {
  margin: 4px 0;
  font-size: 12px;
  color: #666;
}

.login-demo p:first-child {
  font-weight: 500;
  color: #333;
}

@media (max-width: 768px) {
  .login-container {
    padding: 16px;
  }

  .login-card {
    margin: 0;
    padding: 24px;
    max-width: none;
    width: 100%;
  }

  .login-header h1 {
    font-size: 18px;
    line-height: 1.4;
  }

  .login-header p {
    font-size: 12px;
  }

  .login-icon {
    font-size: 36px;
  }
}

@media (max-width: 480px) {
  .login-header h1 {
    font-size: 16px;
  }

  .login-header p {
    font-size: 11px;
  }

  .login-icon {
    font-size: 32px;
  }

  .login-card {
    padding: 20px;
  }
}
